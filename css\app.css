/* 合并所有样式到app.css */
:root {
    /* 现代化高级配色方案 */
    --primary-color: #0062F5;
    --primary-dark: #0050CC;
    --secondary-color: #f8fafc;
    --accent-color: #00C2FF;
    --bg-color: #ffffff;
    --text-color: #0A1F44;
    --text-muted: #4E5D78;
    --light-gray: #EEF2F6;
    --border-radius: 16px;
    --box-shadow: 0 20px 40px rgba(0,0,0,0.06);
    --card-shadow: 0 8px 16px rgba(0,0,0,0.04), 0 16px 24px rgba(0,0,0,0.06);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --section-spacing: 120px;
    --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    --heading-color: #0A1F44;
}

/* 全局样式 */
body {
  font-family: 'Noto Sans SC', 'Inter', sans-serif;
  color: var(--text-color);
  line-height: 1.7;
  overflow-x: hidden;
  width: 100%;
  max-width: 100vw;
  background-color: var(--bg-color);
}

/* 通用容器 */
.section-container {
  max-width: 1200px;
  width: 90%;
  margin: 0 auto;
  padding: 40px 0;
}

/* 标题通用样式 */
.section-title {
  font-size: 2.8rem;
  font-weight: 700;
  margin-bottom: 2.5rem;
  text-align: center;
  position: relative;
  padding-bottom: 1.2rem;
  letter-spacing: -0.02em;
  color: var(--text-color);
}

.section-title:after {
  content: '';
  position: absolute;
  width: 80px;
  height: 4px;
  background: linear-gradient(to right, var(--primary-color), var(--accent-color));
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 98, 245, 0.2);
}
h2 {
  margin-bottom: 1.5rem;
  font-size: 2.2rem;
  font-weight: 700;
  color: var(--primary-color);
}
/* 导航栏样式 */
.site-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: var(--secondary-color);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
  z-index: 1000;
}

.nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 5%;
  max-width: 1400px;
  margin: 0 auto;
  background-color: var(--secondary-color);
}

nav .logo {
  font-size: 1.5rem;
  font-weight: 700;
  letter-spacing: 0.5px;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.nav-links {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  color: black;
}

.nav-links li {
  margin-left: 2.5rem;
}

.nav-links a {
  color: var(--text-color);
  text-decoration: none;
  font-weight: 500;
  position: relative;
  padding: 0.5rem 0;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.nav-links a i {
  font-size: 1.1rem;
  color: var(--primary-color);
}

.nav-links a:after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 0;
  background-color: var(--primary-color);
  transition: var(--transition);
}

.nav-links a:hover {
  color: var(--primary-color);
}

.nav-links a:hover:after {
  width: 100%;
}

.menu-toggle {
  display: none;
  background: var(--primary-color);
  border: none;
  cursor: pointer;
  padding: 10px;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 98, 245, 0.3);
  transition: var(--transition);
  position: relative;
  z-index: 1002;
}

.menu-toggle:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 98, 245, 0.4);
}

.menu-toggle .bar {
  width: 26px;
  height: 3px;
  background-color: white;
  margin: 5px 0;
  border-radius: 2px;
  transition: var(--transition);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 英雄区样式 */
.hero {
  padding: 180px 0 120px;
  text-align: center;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  position: relative;
  overflow: hidden;
  box-shadow: inset 0 -10px 30px rgba(0, 0, 0, 0.03);
}

.hero:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  background-size: cover;
  opacity: 0.05;
  animation: pulse 15s ease-in-out infinite alternate;
}

@keyframes pulse {
  0% { opacity: 0.03; transform: scale(1); }
  100% { opacity: 0.07; transform: scale(1.05); }
}

.hero h1 {
  font-size: 3.8rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  background: linear-gradient(to right, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}


.hero p {
  font-size: 1.5rem;
  color: var(--text-muted);
  margin-bottom: 2.5rem;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

/* 按钮样式 */
.btn {
  display: inline-block;
  padding: 1rem 2.8rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: var(--transition);
  box-shadow: 0 4px 10px rgba(0,0,0,0.1);
  letter-spacing: 0.5px;
}

.btn-primary {
  background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
  color: white;
  border: none;
}

.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(0,0,0,0.15);
}

/* 特性部分 */
.features {
  padding: var(--section-spacing) 0;
  background-color: white;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2.5rem;
  margin-top: 3rem;
}

.feature-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 2.5rem;
  transition: var(--transition);
  box-shadow: var(--card-shadow);
  text-align: center;
  border: 1px solid rgba(0,0,0,0.03);
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.feature-card:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(to right, var(--primary-color), var(--accent-color));
  opacity: 0;
  transition: var(--transition);
  z-index: 2;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  border-color: rgba(0,0,0,0);
}

.feature-card:hover:before {
  opacity: 1;
}

.feature-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--light-gray);
  border-radius: 50%;
}

.feature-icon i {
  font-size: 2rem;
  background: linear-gradient(to right, var(--primary-color), var(--accent-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.feature-card h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.feature-card p {
  color: var(--text-muted);
  line-height: 1.7;
}

/* 截图部分 */
.screenshots {
  padding: var(--section-spacing) 0;
  background: var(--secondary-color);
}

.featured-screenshot-card {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  overflow: hidden;
  margin-bottom: 2.5rem;
}

.featured-screenshot-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid rgba(0,0,0,0.05);
}

.featured-screenshot-title h3 {
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.featured-screenshot-title h3 i {
  color: var(--primary-color);
}

.featured-screenshot-image {
  position: relative;
  padding-top: 56.25%; /* 16:9 比例 */
}

.featured-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.featured-controls {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 2rem;
}

.featured-arrow {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(255,255,255,0.9);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.featured-arrow:hover {
  background: white;
  transform: scale(1.1);
}

.featured-arrow i {
  color: var(--primary-color);
  font-size: 1.1rem;
}

.featured-action {
  position: absolute;
  bottom: 2rem;
  right: 2rem;
}

.featured-button {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 30px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
  box-shadow: 0 4px 10px rgba(0,0,0,0.15);
}

.featured-button:hover {
  background: var(--primary-dark);
  transform: translateY(-3px);
}

.screenshot-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.screenshot-main {
  position: relative;
  padding-top: 56.25%; /* 16:9 比例 */
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--card-shadow);
  background: white;
}

.screenshot-slider {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.screenshot-slider img {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.screenshot-slider img.active {
  opacity: 1;
}

.slider-controls {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 1.5rem;
}

.slider-arrow {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background: rgba(255,255,255,0.9);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.slider-arrow:hover {
  background: white;
  transform: scale(1.1);
}

.slider-indicators {
  position: absolute;
  bottom: 1.5rem;
  right: 1.5rem;
  background: rgba(0,0,0,0.6);
  padding: 0.4rem 0.8rem;
  border-radius: 30px;
  color: white;
  font-weight: 500;
  font-size: 0.9rem;
}

.screenshot-thumbnails {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.screenshot-thumbnails .thumbnail {
  width: 110px;
  height: 70px;
  object-fit: cover;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  opacity: 0.7;
  filter: grayscale(30%);
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  border: 2px solid transparent;
}

.screenshot-thumbnails .thumbnail:hover {
  opacity: 0.9;
  filter: grayscale(0%);
}

.screenshot-thumbnails .thumbnail.active {
  opacity: 1;
  filter: grayscale(0%);
  transform: scale(1.05);
  border-color: var(--primary-color);
}

/* 视频部分 */
.video-section {
  padding: var(--section-spacing) 0;
  background: white;
}

.video-wrapper {
  margin-top: 2.5rem;
}

.video-container {
  position: relative;
  padding-top: 56.25%; /* 16:9 比例 */
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--card-shadow);
}

.video-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}

.video-action {
  text-align: center;
  margin-top: 2rem;
}

.action-button {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 30px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
  box-shadow: 0 4px 10px rgba(0,0,0,0.15);
}

.action-button:hover {
  background: var(--primary-dark);
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(0,0,0,0.2);
}

/* 下载部分 */
.download-section {
  padding: var(--section-spacing) 0;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
}

.download-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 2.5rem;
  margin-bottom: 3rem;
}

.info-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 2rem;
  text-align: center;
  box-shadow: var(--card-shadow);
  transition: var(--transition);
}

.info-card:hover {
  transform: translateY(-5px);
}

.info-icon {
  width: 70px;
  height: 70px;
  margin: 0 auto 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--light-gray);
  border-radius: 50%;
}

.info-icon i {
  font-size: 1.8rem;
  color: var(--primary-color);
}

.info-card h3 {
  font-size: 1.4rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.info-card p {
  color: var(--text-muted);
}

.download-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  justify-content: center;
}

.download-button {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  background: var(--primary-color);
  color: white;
  padding: 1.1rem 2.5rem;
  border-radius: 30px;
  text-decoration: none;
  font-weight: 600;
  transition: var(--transition);
  box-shadow: 0 4px 10px rgba(0,0,0,0.15);
}

.download-button:hover {
  background: var(--primary-dark);
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0,0,0,0.2);
}

.history-button {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  background: white;
  color: var(--primary-color);
  padding: 1.1rem 2.5rem;
  border-radius: 30px;
  text-decoration: none;
  font-weight: 600;
  transition: var(--transition);
  box-shadow: 0 4px 10px rgba(0,0,0,0.08);
  border: 1px solid rgba(0,0,0,0.05);
}

.history-button:hover {
  background: var(--light-gray);
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(0,0,0,0.1);
}

/* 支持部分 */
.support-section {
  padding: var(--section-spacing) 0;
  background: white;
}

.support-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: center;
  margin-top: 3rem;
}

.support-text {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.support-text p {
  font-size: 1.1rem;
  color: var(--text-muted);
  line-height: 1.8;
}

.support-image {
  display: flex;
  justify-content: center;
}

.support-img {
  width: 100%;
  max-width: 400px;
  filter: drop-shadow(0 10px 15px rgba(0,0,0,0.1));
}

/* 模态框样式 */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.7);
  backdrop-filter: blur(5px);
  z-index: 2000;
  overflow: auto;
  align-items: center;
  justify-content: center;
}

.modal-content {
  background: white;
  border-radius: var(--border-radius);
  max-width: 700px;
  width: 90%;
  padding: 2.5rem;
  position: relative;
  margin: 40px 20px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.2);
  max-height: 80vh;
  overflow-y: auto;
}

.close {
  position: absolute;
  top: 1.5rem;
  right: 1.5rem;
  font-size: 1.8rem;
  color: var(--text-muted);
  cursor: pointer;
  transition: var(--transition);
}

.close:hover {
  color: var(--primary-color);
}

.modal-title {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(0,0,0,0.1);
}

.version-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.version-item {
  padding: 1.5rem;
  background: var(--light-gray);
  border-radius: var(--border-radius);
  margin: 10px;
}

.version-item h3 {
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
  color: var(--primary-color);
}

.version-item p {
  margin-bottom: 1rem;
  color: var(--text-muted);
}

.version-content {
  line-height: 1.7;
}

/* 页脚样式 */
footer {
  background: #004d80;
  color: black;
  padding: 3rem 0;
}

.footer-container {
  max-width: 1200px;
  width: 90%;
  margin: 0 auto;
  color: black;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
  color: black;
}

.footer-section h3 {
  font-size: 1.3rem;
  margin-bottom: 1.5rem;
  font-weight: 600;
  position: relative;
  padding-left: 1rem;
  color: var(--primary-color);
}

.footer-section h3:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.3rem;
  height: 1.2rem;
  width: 3px;
  background: var(--primary-color);
  border-radius: 2px;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section ul li {
  margin-bottom: 0.8rem;
}

.footer-section ul li a {
  color: rgba(255,255,255,0.8);
  text-decoration: none;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.footer-section ul li a i {
  color: var(--primary-color);
}

.footer-section ul li a:hover {
  color: white;
  padding-left: 5px;
}

.footer-bottom {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(255,255,255,0.1);
}

.footer-bottom p {
  opacity: 0.7;
  font-size: 0.9rem;
}

/* 全屏滚动样式 */
.fullpage-container {
    height: 100vh;
    overflow-y: scroll;
    scroll-snap-type: y mandatory;
    -webkit-overflow-scrolling: touch;
}

.fullpage-section {
    height: 100vh;
    scroll-snap-align: start;
    scroll-snap-stop: normal;
    position: relative;
    padding: 80px 0; /* 确保内容不被顶部导航遮挡 */
}

/* 导航栏始终置顶 */
#main-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    background-color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  :root {
    --section-spacing: 80px;
  }
  
  .hero h1 {
    font-size: 3.2rem;
  }
  
  .feature-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
  
  .support-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
  
  .support-image {
    order: -1;
  }
  
  .support-img {
    max-width: 300px;
  }
}

@media (max-width: 800px) {
  :root {
    --section-spacing: 60px;
  }
  
  .nav {
    padding: 1rem 2rem;
  }
  
  .nav-links {
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transform: translateY(-10px);
    opacity: 0;
    pointer-events: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1001;
  }
  
  .nav-links.active {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
  }
  
  .nav-links li {
    margin: 1.2rem 0;
    width: 100%;
    text-align: center;
  }
  
  .nav-links a {
    font-size: 1.2rem;
    padding: 1rem;
    width: 100%;
    display: flex;
    justify-content: center;
    border-radius: 12px;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .nav-links a i {
    font-size: 1.3rem;
    margin-right: 8px;
  }
  
  .nav-links a:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 98, 245, 0.2);
  }
  
  .nav-links a:hover i {
    color: white;
  }
  
  .menu-toggle {
    display: block;
    z-index: 1002;
  }
  
  .menu-toggle.active .bar:nth-child(1) {
    transform: rotate(-45deg) translate(-6px, 6px);
    width: 28px;
  }
  
  .menu-toggle.active .bar:nth-child(2) {
    opacity: 0;
    transform: translateX(-10px);
  }
  
  .menu-toggle.active .bar:nth-child(3) {
    transform: rotate(45deg) translate(-6px, -6px);
    width: 28px;
  }
  
  .menu-toggle.active {
    background: var(--primary-dark);
    box-shadow: 0 4px 16px rgba(0, 98, 245, 0.5);
  }
  
  .hero {
    padding: 150px 0 80px;
  }
  
  .hero h1 {
    font-size: 2.5rem;
  }
  
  .hero p {
    font-size: 1.2rem;
  }
  
  .section-title {
    font-size: 2.2rem;
  }
  
  .featured-screenshot-title {
    padding: 1rem 1.5rem;
  }
  
  .featured-arrow, .slider-arrow {
    width: 40px;
    height: 40px;
  }
  
  .download-button, .history-button, .action-button {
    width: 100%;
    justify-content: center;
    padding: 1rem 0;
  }
}

@media (max-width: 480px) {
  .hero h1 {
    font-size: 2.2rem;
  }
  
  .section-title {
    font-size: 1.8rem;
  }
  
  .feature-icon {
    width: 60px;
    height: 60px;
  }
  
  .feature-card h3 {
    font-size: 1.3rem;
  }
  
  .screenshot-thumbnails .thumbnail {
    width: 80px;
    height: 50px;
  }
}

/* 附加动画效果 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(30px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.visible {
  animation: fadeIn 0.6s ease forwards, slideUp 0.6s ease forwards;
}

/* 暗黑模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-color: #0f172a;
    --text-color: #f1f5f9;
    --text-muted: #94a3b8;
    --light-gray: #1e293b;
    --card-shadow: 0 4px 6px rgba(0,0,0,0.2), 0 10px 15px rgba(0,0,0,0.2);
    --secondary-color: #1e293b;
  }

  .site-header {
    background-color: rgba(15, 23, 42, 0.8);
    backdrop-filter: blur(10px);
  }
  
  .nav {
    background-color: transparent;
  }
  
  .menu-toggle {
    background: var(--primary-color);
    box-shadow: 0 4px 16px rgba(0, 98, 245, 0.5);
  }
  
  .menu-toggle .bar {
    background-color: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  }
  
  .nav-links {
    background-color: rgba(15, 23, 42, 0.95);
  }
  
  .hero {
    background: linear-gradient(135deg, #1e293b, #0f172a);
  }
  
  .feature-card, .info-card, .modal-content, .featured-screenshot-card {
    background: #1e293b;
    border-color: rgba(255,255,255,0.05);
  }
  
  .screenshot-main, .video-container {
    background: #1e293b;
  }
  
  .featured-arrow, .slider-arrow {
    background: rgba(30, 41, 59, 0.9);
  }
  
  .history-button {
    background: #1e293b;
    color: var(--accent-color);
    border-color: rgba(255,255,255,0.1);
  }
  
  .version-item {
    background: #0f172a;
  }
  
  footer {
    background: #0f172a;
  }
}
