{"$ref": "#/definitions/post", "definitions": {"post": {"type": "object", "properties": {"publishDate": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "string", "format": "date"}, {"type": "integer", "format": "unix-time"}]}, "updateDate": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "string", "format": "date"}, {"type": "integer", "format": "unix-time"}]}, "draft": {"type": "boolean"}, "title": {"type": "string"}, "excerpt": {"type": "string"}, "image": {"type": "string"}, "category": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}, "author": {"type": "string"}, "metadata": {"type": "object", "properties": {"title": {"type": "string"}, "ignoreTitleTemplate": {"type": "boolean"}, "canonical": {"type": "string", "format": "uri"}, "robots": {"type": "object", "properties": {"index": {"type": "boolean"}, "follow": {"type": "boolean"}}, "additionalProperties": false}, "description": {"type": "string"}, "openGraph": {"type": "object", "properties": {"url": {"type": "string"}, "siteName": {"type": "string"}, "images": {"type": "array", "items": {"type": "object", "properties": {"url": {"type": "string"}, "width": {"type": "number"}, "height": {"type": "number"}}, "required": ["url"], "additionalProperties": false}}, "locale": {"type": "string"}, "type": {"type": "string"}}, "additionalProperties": false}, "twitter": {"type": "object", "properties": {"handle": {"type": "string"}, "site": {"type": "string"}, "cardType": {"type": "string"}}, "additionalProperties": false}}, "additionalProperties": false}, "$schema": {"type": "string"}}, "required": ["title"], "additionalProperties": false}}, "$schema": "http://json-schema.org/draft-07/schema#"}