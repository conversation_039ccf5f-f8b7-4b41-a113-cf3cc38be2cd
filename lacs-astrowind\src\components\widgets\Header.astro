---
import { Icon } from 'astro-icon/components';
import Logo from '~/components/Logo.astro';
import ToggleTheme from '~/components/common/ToggleTheme.astro';
import ToggleMenu from '~/components/common/ToggleMenu.astro';
import Button from '~/components/ui/Button.astro';

import { getHomePermalink } from '~/utils/permalinks';
import { trimSlash } from '~/utils/permalinks';
import type { CallToAction } from '~/types';

interface Link {
  text?: string;
  href?: string;
  ariaLabel?: string;
  icon?: string;
}

interface MenuLink extends Link {
  links?: Array<MenuLink>;
}

export interface Props {
  id?: string;
  links?: Array<MenuLink>;
  actions?: Array<CallToAction>;
  isSticky?: boolean;
  isDark?: boolean;
  isFullWidth?: boolean;
  showToggleTheme?: boolean;
  showRssFeed?: boolean;
  position?: string;
}

const {
  id = 'header',
  links = [],
  actions = [],
  isSticky = false,
  isDark = false,
  isFullWidth = false,
  showToggleTheme = false,
  showRssFeed = false,
  position = 'center',
} = Astro.props;

const currentPath = `/${trimSlash(new URL(Astro.url).pathname)}`;
---

<header
  class:list={[
    'fixed',
    { dark: isDark },
    'top-0 z-50 flex-none mx-auto w-full bg-white/90 dark:bg-slate-900/90 backdrop-blur-md border-b border-gray-50/20 dark:border-slate-800/20 transition-[opacity] ease-in-out',
  ]}
  {...isSticky ? { 'data-aw-sticky-header': true } : {}}
  {...id ? { id } : {}}
>
  <div class="absolute inset-0"></div>
  <div
    class:list={[
      'relative text-default py-3 px-3 md:px-6 mx-auto w-full',
      {
        'md:flex md:justify-between': position !== 'center',
      },
      {
        'md:grid md:grid-cols-3 md:items-center': position === 'center',
      },
      {
        'max-w-7xl': !isFullWidth,
      },
    ]}
  >
    <div class:list={[{ 'mr-auto rtl:mr-0 rtl:ml-auto': position === 'right' }, 'flex justify-between items-center w-full']}>
      <a class="flex items-center" href={getHomePermalink()}>
        <Logo />
      </a>

      <!-- 移动端功能按钮区域 -->
      <div class="flex items-center gap-1 md:hidden">
        <!-- 主题切换按钮 -->
        {showToggleTheme && (
          <div class="flex items-center">
            <ToggleTheme iconClass="w-5 h-5" />
          </div>
        )}

        <!-- 返回顶部按钮 -->
        <button
          id="mobile-back-to-top"
          class="text-muted dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 active:bg-gray-200 dark:active:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 rounded-lg p-2 inline-flex items-center justify-center min-w-[44px] min-h-[44px] transition-all duration-150"
          aria-label="返回顶部"
          title="返回顶部"
        >
          <Icon name="tabler:arrow-up" class="w-5 h-5" />
        </button>

        <!-- 去底部按钮 -->
        <button
          id="mobile-go-to-bottom"
          class="text-muted dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 active:bg-gray-200 dark:active:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 rounded-lg p-2 inline-flex items-center justify-center min-w-[44px] min-h-[44px] transition-all duration-150"
          aria-label="去底部"
          title="去底部"
        >
          <Icon name="tabler:arrow-down" class="w-5 h-5" />
        </button>

        <!-- 菜单切换按钮 -->
        <div class="flex items-center">
          <ToggleMenu />
        </div>
      </div>
    </div>
    <nav
      class="items-center w-full md:w-auto hidden md:flex md:mx-5 text-default overflow-y-auto overflow-x-hidden md:overflow-y-visible md:overflow-x-auto md:justify-self-center"
      aria-label="Main navigation"
    >
      <ul
        class="flex flex-col md:flex-row md:self-center w-full md:w-auto text-xl md:text-[0.9375rem] tracking-[0.01rem] font-medium md:justify-center"
      >
        {
          links.map(({ text, href, links }) => (
            <li class={links?.length ? 'dropdown' : ''}>
              {links?.length ? (
                <>
                  <button
                    type="button"
                    class="hover:text-link dark:hover:text-white px-4 py-3 flex items-center whitespace-nowrap"
                  >
                    {text}{' '}
                    <Icon name="tabler:chevron-down" class="w-3.5 h-3.5 ml-0.5 rtl:ml-0 rtl:mr-0.5 hidden md:inline" />
                  </button>
                  <ul class="dropdown-menu md:backdrop-blur-md dark:md:bg-dark rounded md:absolute pl-4 md:pl-0 md:hidden font-medium md:bg-white/90 md:min-w-[200px] drop-shadow-xl">
                    {links.map(({ text: text2, href: href2 }) => (
                      <li>
                        <a
                          class:list={[
                            'first:rounded-t last:rounded-b md:hover:bg-gray-100 hover:text-link dark:hover:text-white dark:hover:bg-gray-700 py-2 px-5 block whitespace-no-wrap',
                            { 'aw-link-active': href2 === currentPath },
                          ]}
                          href={href2}
                        >
                          {text2}
                        </a>
                      </li>
                    ))}
                  </ul>
                </>
              ) : (
                <a
                  class:list={[
                    'hover:text-link dark:hover:text-white px-4 py-3 flex items-center whitespace-nowrap',
                    { 'aw-link-active': href === currentPath },
                  ]}
                  href={href}
                >
                  {text}
                </a>
              )}
            </li>
          ))
        }
      </ul>
    </nav>
    <div
      class:list={[
        { 'ml-auto rtl:ml-0 rtl:mr-auto': position === 'left' },
        'hidden md:self-center md:flex items-center md:mb-0 fixed w-full md:w-auto md:static justify-end left-0 rtl:left-auto rtl:right-0 bottom-0 p-3 md:p-0 md:justify-self-end',
      ]}
    >
      <div class="items-center flex justify-between w-full md:w-auto">
        <!-- 桌面端功能按钮 -->
        <div class="hidden md:flex items-center space-x-1">
          {showToggleTheme && <ToggleTheme iconClass="w-5 h-5" />}

          <!-- 桌面端返回顶部按钮 -->
          <button
            id="desktop-back-to-top"
            class="text-muted dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5 inline-flex items-center"
            aria-label="返回顶部"
            title="返回顶部"
          >
            <Icon name="tabler:arrow-up" class="w-5 h-5" />
          </button>

          <!-- 桌面端去底部按钮 -->
          <button
            id="desktop-go-to-bottom"
            class="text-muted dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5 inline-flex items-center"
            aria-label="去底部"
            title="去底部"
          >
            <Icon name="tabler:arrow-down" class="w-5 h-5" />
          </button>
        </div>
        {
          actions?.length ? (
            <span class="ml-4 rtl:ml-0 rtl:mr-4">
              {actions.map((btnProps) => (
                <Button {...btnProps} class="ml-2 py-2.5 px-5.5 md:px-6 font-semibold shadow-none text-sm w-auto" />
              ))}
            </span>
          ) : (
            ''
          )
        }
      </div>
    </div>
  </div>
</header>

<script>
  // 返回顶部和去底部功能
  document.addEventListener('DOMContentLoaded', () => {
    // 移动端按钮
    const mobileBackToTopButton = document.getElementById('mobile-back-to-top');
    const mobileGoToBottomButton = document.getElementById('mobile-go-to-bottom');

    // 桌面端按钮
    const desktopBackToTopButton = document.getElementById('desktop-back-to-top');
    const desktopGoToBottomButton = document.getElementById('desktop-go-to-bottom');

    // 返回顶部功能
    const scrollToTop = () => {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    };

    // 去底部功能
    const scrollToBottom = () => {
      window.scrollTo({
        top: document.body.scrollHeight,
        behavior: 'smooth'
      });
    };

    // 为所有按钮添加事件监听器
    if (mobileBackToTopButton) {
      mobileBackToTopButton.addEventListener('click', scrollToTop);
      // 添加触摸反馈
      mobileBackToTopButton.addEventListener('touchstart', () => {
        mobileBackToTopButton.style.transform = 'scale(0.95)';
      });
      mobileBackToTopButton.addEventListener('touchend', () => {
        setTimeout(() => {
          mobileBackToTopButton.style.transform = 'scale(1)';
        }, 100);
      });
    }

    if (mobileGoToBottomButton) {
      mobileGoToBottomButton.addEventListener('click', scrollToBottom);
      // 添加触摸反馈
      mobileGoToBottomButton.addEventListener('touchstart', () => {
        mobileGoToBottomButton.style.transform = 'scale(0.95)';
      });
      mobileGoToBottomButton.addEventListener('touchend', () => {
        setTimeout(() => {
          mobileGoToBottomButton.style.transform = 'scale(1)';
        }, 100);
      });
    }

    if (desktopBackToTopButton) {
      desktopBackToTopButton.addEventListener('click', scrollToTop);
    }

    if (desktopGoToBottomButton) {
      desktopGoToBottomButton.addEventListener('click', scrollToBottom);
    }

    // 移动端菜单关闭优化
    const header = document.getElementById('header');
    const toggleMenuButton = document.querySelector('[data-aw-toggle-menu]');

    if (header && toggleMenuButton) {
      // 点击菜单外部关闭菜单
      document.addEventListener('click', (e) => {
        const target = e.target as Node;
        if (header.classList.contains('expanded') &&
            target &&
            !header.contains(target) &&
            !toggleMenuButton.contains(target)) {
          header.classList.remove('expanded');
          toggleMenuButton.classList.remove('expanded');
        }
      });

      // ESC键关闭菜单
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && header.classList.contains('expanded')) {
          header.classList.remove('expanded');
          toggleMenuButton.classList.remove('expanded');
        }
      });
    }
  });
</script>
