<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小米设备代码浏览器</title>
    <!-- 引入Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <!-- 引入Inter字体 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- 配置Tailwind自定义主题 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#165DFF',
                        secondary: '#0A2463',
                        accent: '#3E92CC',
                        neutral: '#F5F7FA',
                        'neutral-dark': '#2A2D34',
                        'gray-50': '#F9FAFB',
                        'gray-100': '#F3F4F6',
                        'gray-200': '#E5E7EB',
                        'gray-300': '#D1D5DB',
                        'gray-400': '#9CA3AF',
                        'gray-500': '#6B7280',
                        'gray-600': '#4B5563',
                        'gray-700': '#374151',
                        'gray-800': '#1F2937',
                        'gray-900': '#111827',
                    },
                    fontFamily: {
                        inter: ['Inter', 'sans-serif'],
                    },
                    boxShadow: {
                        'card': '0 4px 12px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.1)',
                        'card-hover': '0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 10px rgba(0, 0, 0, 0.06)',
                        'dropdown': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
                    },
                    transitionProperty: {
                        'height': 'height',
                        'spacing': 'margin, padding',
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.3s ease-in-out',
                        'slide-up': 'slideUp 0.4s ease-out',
                        'scale': 'scale 0.2s ease-out',
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        },
                        scale: {
                            '0%': { transform: 'scale(0.95)', opacity: '0' },
                            '100%': { transform: 'scale(1)', opacity: '1' },
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- 自定义工具类 -->
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .device-card {
                @apply bg-white rounded-xl overflow-hidden transition-all duration-300 shadow-card hover:shadow-card-hover hover:-translate-y-1;
            }
            .device-card-header {
                @apply px-6 py-4 border-b border-gray-100 flex justify-between items-center;
            }
            .device-card-body {
                @apply p-6;
            }
            .btn {
                @apply px-4 py-2 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
            }
            .btn-primary {
                @apply bg-primary text-white hover:bg-primary/90 focus:ring-primary/50;
            }
            .btn-outline {
                @apply border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-200;
            }
            .btn-icon {
                @apply w-10 h-10 flex items-center justify-center rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-50 transition-colors;
            }
            .tag {
                @apply px-2 py-1 rounded text-xs font-medium;
            }
            .tag-primary {
                @apply bg-primary/10 text-primary;
            }
            .tag-secondary {
                @apply bg-secondary/10 text-secondary;
            }
            .tag-accent {
                @apply bg-accent/10 text-accent;
            }
            .badge {
                @apply inline-flex items-center justify-center px-2 py-1 text-xs font-medium rounded-full;
            }
            .badge-success {
                @apply bg-green-100 text-green-800;
            }
            .badge-warning {
                @apply bg-yellow-100 text-yellow-800;
            }
            .badge-danger {
                @apply bg-red-100 text-red-800;
            }
            .badge-info {
                @apply bg-blue-100 text-blue-800;
            }
            .badge-purple {
                @apply bg-purple-100 text-purple-800;
            }
            .animate-fade-in {
                animation: fadeIn 0.3s ease-in-out;
            }
            .animate-slide-up {
                animation: slideUp 0.4s ease-out;
            }
            .animate-scale {
                animation: scale 0.2s ease-out;
            }
            .scrollbar-hide::-webkit-scrollbar {
                display: none;
            }
            .scrollbar-hide {
                -ms-overflow-style: none;
                scrollbar-width: none;
            }
            .text-balance {
                text-wrap: balance;
            }
            .backdrop-blur-sm {
                backdrop-filter: blur(4px);
            }
        }
    </style>
</head>
<body class="font-inter bg-neutral min-h-screen flex flex-col">
    <!-- 导航栏 -->
    <header class="bg-white/80 backdrop-blur-sm shadow-sm sticky top-0 z-50 transition-all duration-300" id="navbar">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="#" class="flex items-center">
                        <div class="bg-primary/10 p-2 rounded-lg mr-3">
                            <i class="fa fa-mobile-phone text-primary text-xl"></i>
                        </div>
                        <span class="text-xl font-bold text-secondary">Micode</span>
                    </a>
                </div>
                
                <!-- 搜索框 -->
                <div class="relative w-full max-w-md mx-4">
                    <div class="relative">
                        <input type="text" id="search-input" 
                            class="w-full py-2.5 pl-10 pr-12 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-all duration-200 text-gray-800"
                            placeholder="搜索设备型号、代码或日期...">
                        <i class="fa fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        <button id="clear-search" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors hidden">
                            <i class="fa fa-times-circle"></i>
                        </button>
                    </div>
                </div>
                
                <!-- 导航按钮 -->
                <div class="flex items-center space-x-2">
                    <button id="theme-toggle" class="btn-icon">
                        <i class="fa fa-moon-o"></i>
                    </button>
                    <button id="filter-toggle" class="btn-icon md:hidden">
                        <i class="fa fa-filter"></i>
                    </button>
                    <button id="refresh-btn" class="btn btn-primary flex items-center">
                        <i class="fa fa-refresh mr-2"></i>
                        <span class="hidden sm:inline">刷新数据</span>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 筛选栏 -->
        <div id="filter-bar" class="bg-white border-t border-gray-100 px-4 py-3 hidden md:block transition-all duration-300">
            <div class="container mx-auto flex flex-wrap items-center gap-4">
                <div class="flex items-center">
                    <label for="unlock-filter" class="text-sm font-medium text-gray-700 mr-3">解锁方式:</label>
                    <div class="relative">
                        <select id="unlock-filter" class="py-2 px-3 pr-8 rounded-lg border border-gray-200 text-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary bg-white appearance-none">
                            <option value="all">全部</option>
                            <option value="official">官方解锁工具</option>
                            <option value="hout">澎湃解锁工具箱</option>
                            <option value="answer">社区答题解锁</option>
                            <option value="9008">9008解锁</option>
                            <option value="mtk">联发科解锁</option>
                        </select>
                        <i class="fa fa-chevron-down absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none"></i>
                    </div>
                </div>
                
                <div class="flex items-center">
                    <label for="date-filter" class="text-sm font-medium text-gray-700 mr-3">发布日期:</label>
                    <div class="relative">
                        <select id="date-filter" class="py-2 px-3 pr-8 rounded-lg border border-gray-200 text-sm focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary bg-white appearance-none">
                            <option value="all">全部</option>
                            <option value="2025">2025年</option>
                            <option value="2024">2024年</option>
                            <option value="2023">2023年</option>
                            <option value="2022">2022年</option>
                            <option value="2021">2021年</option>
                            <option value="2020">2020年</option>
                            <option value="older">更早</option>
                        </select>
                        <i class="fa fa-chevron-down absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none"></i>
                    </div>
                </div>
                
                <div class="flex items-center ml-auto">
                    <span id="device-count" class="text-sm text-gray-600">显示 0/0 台设备</span>
                </div>
            </div>
        </div>
    </header>

    <!-- 移动端筛选菜单 -->
    <div id="mobile-filter" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="bg-white h-full w-4/5 max-w-sm p-6 shadow-lg transform transition-transform duration-300 -translate-x-full" id="mobile-filter-panel">
            <div class="flex justify-between items-center mb-6">
                <h3 class="font-bold text-lg text-secondary">筛选条件</h3>
                <button id="close-filter" class="text-gray-500 hover:text-gray-700">
                    <i class="fa fa-times"></i>
                </button>
            </div>
            
            <div class="space-y-6">
                <div>
                    <label for="unlock-filter-mobile" class="block text-sm font-medium text-gray-700 mb-2">解锁方式:</label>
                    <div class="relative">
                        <select id="unlock-filter-mobile" class="w-full py-3 px-4 pr-8 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary appearance-none">
                            <option value="all">全部</option>
                            <option value="official">官方解锁工具</option>
                            <option value="hout">澎湃解锁工具箱</option>
                            <option value="answer">社区答题解锁</option>
                            <option value="9008">9008解锁</option>
                            <option value="mtk">联发科解锁</option>
                        </select>
                        <i class="fa fa-chevron-down absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none"></i>
                    </div>
                </div>
                
                <div>
                    <label for="date-filter-mobile" class="block text-sm font-medium text-gray-700 mb-2">发布日期:</label>
                    <div class="relative">
                        <select id="date-filter-mobile" class="w-full py-3 px-4 pr-8 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary appearance-none">
                            <option value="all">全部</option>
                            <option value="2025">2025年</option>
                            <option value="2024">2024年</option>
                            <option value="2023">2023年</option>
                            <option value="2022">2022年</option>
                            <option value="2021">2021年</option>
                            <option value="2020">2020年</option>
                            <option value="older">更早</option>
                        </select>
                        <i class="fa fa-chevron-down absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none"></i>
                    </div>
                </div>
                
                <div class="pt-6 border-t border-gray-200">
                    <button id="apply-filter" class="w-full btn btn-primary py-3 flex items-center justify-center">
                        <i class="fa fa-filter mr-2"></i> 应用筛选
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容区 -->
    <main class="flex-grow container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 页面标题 -->
        <div class="mb-8 animate-fade-in">
            <div class="flex flex-col md:flex-row md:items-end justify-between">
                <div>
                    <h1 class="text-[clamp(1.5rem,3vw,2.5rem)] font-bold text-gray-800 mb-2">小米设备代码数据库</h1>
                    <p class="text-gray-600 max-w-3xl">浏览并查找小米设备的详细信息，包括设备代号、发布日期和解锁方法。我们的数据库包含了小米全系列产品的完整信息。</p>
                </div>
                <div class="mt-4 md:mt-0 flex space-x-2">
                    <button class="btn btn-outline flex items-center" id="view-grid">
                        <i class="fa fa-th-large mr-2"></i> 网格视图
                    </button>
                    <button class="btn btn-outline flex items-center" id="view-list">
                        <i class="fa fa-list mr-2"></i> 列表视图
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 animate-fade-in">
            <div class="bg-white rounded-xl p-6 shadow-card hover:shadow-card-hover transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mr-4">
                        <i class="fa fa-mobile-phone text-xl text-primary"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 mb-1">设备总数</p>
                        <p class="text-2xl font-bold text-gray-800" id="total-devices">0</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-xl p-6 shadow-card hover:shadow-card-hover transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                        <i class="fa fa-check-circle text-xl text-green-600"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 mb-1">官方可解锁</p>
                        <p class="text-2xl font-bold text-gray-800" id="official-count">0</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-xl p-6 shadow-card hover:shadow-card-hover transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                        <i class="fa fa-calendar text-xl text-purple-600"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 mb-1">2025年发布</p>
                        <p class="text-2xl font-bold text-gray-800" id="2025-count">0</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-xl p-6 shadow-card hover:shadow-card-hover transition-all duration-300">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                        <i class="fa fa-code text-xl text-blue-600"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 mb-1">设备代号</p>
                        <p class="text-2xl font-bold text-gray-800" id="dh-count">0</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 加载状态 -->
        <div id="loading" class="flex flex-col items-center justify-center py-16 animate-fade-in">
            <div class="w-16 h-16 border-4 border-primary/30 border-t-primary rounded-full animate-spin mb-4"></div>
            <p class="text-gray-500 text-lg">正在加载设备数据...</p>
        </div>
        
        <!-- 错误状态 -->
        <div id="error" class="hidden flex flex-col items-center justify-center py-16 animate-fade-in">
            <div class="text-5xl text-red-400 mb-4">
                <i class="fa fa-exclamation-triangle"></i>
            </div>
            <h2 class="text-xl font-bold text-gray-800 mb-2">加载失败</h2>
            <p class="text-gray-500 mb-4">无法获取设备数据，请稍后重试。</p>
            <button id="retry-btn" class="btn btn-primary flex items-center">
                <i class="fa fa-refresh mr-2"></i> 重试
            </button>
        </div>
        
        <!-- 内容区 -->
        <div id="content" class="hidden">
            <!-- 设备网格 -->
            <div id="devices-grid" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                <!-- 设备卡片将通过JavaScript动态生成 -->
            </div>

            <!-- 加载更多按钮 -->
            <div id="load-more" class="flex justify-center mt-12 hidden">
                <button id="load-more-btn" class="btn btn-primary px-8 py-3 flex items-center animate-scale">
                    <i class="fa fa-refresh mr-2"></i>
                    <span>加载更多设备</span>
                </button>
            </div>
            
            <!-- 空状态 -->
            <div id="empty-state" class="hidden flex flex-col items-center justify-center py-20 animate-fade-in">
                <div class="text-6xl text-gray-300 mb-6">
                    <i class="fa fa-search"></i>
                </div>
                <h2 class="text-2xl font-bold text-gray-800 mb-3">未找到匹配的设备</h2>
                <p class="text-gray-500 mb-8 max-w-md text-center">请尝试使用不同的搜索条件或筛选器。您可以尝试扩大搜索范围或检查您的输入是否正确。</p>
                <button id="reset-filters" class="btn btn-outline flex items-center">
                    <i class="fa fa-refresh mr-2"></i> 重置筛选器
                </button>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-white border-t border-gray-200 py-8">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-6 md:mb-0">
                    <div class="flex items-center mb-3">
                        <div class="bg-primary/10 p-1.5 rounded-lg mr-2">
                            <i class="fa fa-mobile-phone text-primary"></i>
                        </div>
                        <span class="text-lg font-bold text-secondary">小米设备代码浏览器</span>
                    </div>
                    <p class="text-sm text-gray-500 max-w-md">提供全面的小米设备信息和解锁方法，帮助您更好地了解和管理您的设备。</p>
                </div>
                <div class="flex flex-col items-center md:items-end">
                    <div class="flex space-x-4 mb-4">
                        <a href="#" class="text-gray-500 hover:text-primary transition-colors">
                            <i class="fa fa-github text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-500 hover:text-primary transition-colors">
                            <i class="fa fa-twitter text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-500 hover:text-primary transition-colors">
                            <i class="fa fa-envelope text-xl"></i>
                        </a>
                    </div>
                    <p class="text-sm text-gray-500">© 2025 小米设备代码浏览器. 保留所有权利.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- 设备详情模态框 -->
    <div id="device-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
        <div class="bg-white rounded-xl shadow-xl max-w-3xl w-full max-h-[90vh] overflow-auto transform transition-all duration-300 scale-95 opacity-0" id="modal-content">
            <div class="p-6">
                <div class="flex justify-between items-start mb-6">
                    <h3 id="modal-title" class="text-2xl font-bold text-gray-800"></h3>
                    <button id="close-modal" class="text-gray-500 hover:text-gray-700 transition-colors">
                        <i class="fa fa-times text-xl"></i>
                    </button>
                </div>
                
                <div id="modal-body" class="space-y-8">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="animate-slide-up" style="animation-delay: 0.1s">
                            <h4 class="text-sm font-medium text-gray-500 mb-2">设备型号</h4>
                            <p id="modal-model" class="font-medium text-lg text-gray-800"></p>
                        </div>
                        <div class="animate-slide-up" style="animation-delay: 0.2s">
                            <h4 class="text-sm font-medium text-gray-500 mb-2">设备代号</h4>
                            <p id="modal-dh" class="font-medium text-lg text-gray-800"></p>
                        </div>
                        <div class="animate-slide-up" style="animation-delay: 0.3s">
                            <h4 class="text-sm font-medium text-gray-500 mb-2">发布日期</h4>
                            <p id="modal-date" class="font-medium text-lg text-gray-800"></p>
                        </div>
                        <div class="animate-slide-up" style="animation-delay: 0.4s">
                            <h4 class="text-sm font-medium text-gray-500 mb-2">解锁方式</h4>
                            <div id="modal-unlock" class="font-medium text-lg text-gray-800"></div>
                        </div>
                    </div>
                    
                    <div class="border-t border-gray-200 pt-8 animate-slide-up" style="animation-delay: 0.5s">
                        <h4 class="text-sm font-medium text-gray-500 mb-4">解锁方法</h4>
                        <div id="modal-unlock-method" class="bg-gray-50 p-6 rounded-xl text-sm space-y-4 text-gray-700">
                            <!-- 解锁步骤将通过JavaScript动态生成 -->
                        </div>
                    </div>
                    
                    <div class="border-t border-gray-200 pt-8 animate-slide-up" style="animation-delay: 0.6s">
                        <h4 class="text-sm font-medium text-gray-500 mb-4">相关设备</h4>
                        <div id="related-devices" class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <!-- 相关设备将通过JavaScript动态生成 -->
                        </div>
                    </div>
                    
                    <div class="border-t border-gray-200 pt-8 animate-slide-up" style="animation-delay: 0.6s">
                        <h4 class="text-sm font-medium text-gray-500 mb-4">注意事项</h4>
                        <div class="bg-red-50 p-6 rounded-xl text-sm text-red-700 space-y-4">
                            <p class="flex items-start">
                                <i class="fa fa-exclamation-circle text-red-500 mr-3 mt-0.5"></i>
                                <span>解锁设备将清除所有数据，请务必备份重要数据。</span>
                            </p>
                            <p class="flex items-start">
                                <i class="fa fa-exclamation-circle text-red-500 mr-3 mt-0.5"></i>
                                <span>不正确的解锁操作可能导致设备无法正常使用。</span>
                            </p>
                            <p class="flex items-start">
                                <i class="fa fa-exclamation-circle text-red-500 mr-3 mt-0.5"></i>
                                <span>解锁后设备可能失去保修资格，请谨慎操作。</span>
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="mt-10 animate-slide-up" style="animation-delay: 0.7s">
                    <button id="close-modal-btn" class="btn btn-primary w-full py-3 flex items-center justify-center">
                        <i class="fa fa-check-circle mr-2"></i> 了解并关闭
                    </button>
                </div>
            </div>
        </div>
    </footer>

    <script src="../js/micode.js"></script>
    </body>
</html>