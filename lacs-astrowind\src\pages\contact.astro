---
import Layout from '~/layouts/PageLayout.astro';
import Hero from '~/components/widgets/Hero.astro';
import Content from '~/components/widgets/Content.astro';
import Features from '~/components/widgets/Features.astro';
import CallToAction from '~/components/widgets/CallToAction.astro';
import CustomStyles from '~/components/CustomStyles.astro';

// 导入数据文件
import mediaPlatformsData from '~/data/mediaPlatforms.json';
import contactInfoData from '~/data/contactInfo.json';
import groupChatsData from '~/data/groupChats.json';

const metadata = {
  title: '联系我们 - 领创工作室',
  description: '联系领创工作室，获取技术支持、商务合作或其他服务。我们提供多种联系方式，24小时内回复您的咨询。了解我们的媒体平台和官方群聊。',
  keywords: '联系我们,技术支持,商务合作,领创工作室,客服,媒体平台,官方群聊',
};

// 转换联系方式数据格式
const contactMethods = contactInfoData.map(contact => ({
  title: contact.title,
  description: contact.description,
  info: contact.info,
  action: contact.action,
  icon: contact.title === '电子邮件' ? 'tabler:mail' :
        contact.title === 'QQ' ? 'tabler:brand-qq' :
        contact.title === '微信' ? 'tabler:brand-wechat' : 'tabler:message',
  color: contact.title === '电子邮件' ? 'primary' :
         contact.title === 'QQ' ? 'secondary' : 'accent',
}));

// 转换媒体平台数据格式
const mediaPlatforms = mediaPlatformsData.map(platform => ({
  title: platform.name,
  description: `${platform.account}<br><strong>ID: ${platform.accountId}</strong>`,
  icon: platform.id === 'bilibili' ? 'tabler:brand-bilibili' :
        platform.id === 'wechat' ? 'tabler:brand-wechat' :
        platform.id === 'coolapk' ? 'tabler:device-mobile' :
        platform.id === 'csdn' ? 'tabler:code' :
        platform.id === 'mt' ? 'tabler:tools' :
        platform.id === 'xiaomi' ? 'tabler:device-mobile' : 'tabler:world',
  callToAction: {
    text: '访问平台',
    href: platform.link,
    variant: 'link' as const,
  },
}));

// 转换群聊数据格式
const qqGroups = groupChatsData.map(group => ({
  title: group.name,
  description: group.limit || '官方群聊',
  info: `群号：${group.groupNumber}`,
  action: group.joinLink,
  icon: group.name.includes('频道') ? 'tabler:brand-discord' : 'tabler:users',
  color: 'primary',
}));
---

<Layout metadata={metadata}>
  <CustomStyles />

  <!-- Hero Section -->
  <Hero
    tagline="联系我们"
    title="随时为您提供专业服务"
    subtitle="领创工作室致力于为用户提供优质的技术支持和服务。无论您有任何问题或需求，我们都会及时回复并提供专业的解决方案。关注我们的媒体平台，获取最新资讯和技术分享。"
    actions={[
      {
        variant: 'primary',
        text: '立即联系',
        href: '#contact-methods',
        icon: 'tabler:arrow-down',
      },
      {
        variant: 'secondary',
        text: '关注我们',
        href: '#media-platforms',
        icon: 'tabler:share',
      },
    ]}
  />

  <!-- 媒体平台 -->
  <Features
    id="media-platforms"
    title="关注我们的媒体平台"
    subtitle="获取最新资讯、技术分享和项目动态"
    tagline="媒体平台"
    items={mediaPlatforms}
    columns={3}
  >
    <Fragment slot="bg">
      <div class="absolute inset-0 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-slate-800 dark:to-slate-900"></div>
    </Fragment>
  </Features>

  <!-- 联系方式 -->
  <Features
    id="contact-methods"
    title="多种联系方式"
    subtitle="选择最适合您的联系方式，我们会尽快回复"
    tagline="联系方式"
    items={contactMethods.map(method => ({
      title: method.title,
      description: `${method.description}<br><strong>${method.info}</strong>`,
      icon: method.icon,
      callToAction: {
        text: '立即联系',
        href: method.action,
        target: method.action.startsWith('http') ? '_blank' : '_self',
        variant: 'link',
      },
    }))}
    columns={3}
  >
    <Fragment slot="bg">
      <div class="absolute inset-0 bg-blue-50 dark:bg-slate-800"></div>
    </Fragment>
  </Features>

  <!-- QQ群聊 -->
  <Features
    title="加入我们的社区"
    subtitle="与其他用户交流，获取最新资讯和技术支持"
    tagline="官方群聊"
    items={qqGroups.map(group => ({
      title: group.title,
      description: `${group.description}<br><strong>${group.info}</strong>`,
      icon: group.icon,
      callToAction: {
        text: '加入群聊',
        href: group.action,
        target: '_blank',
        variant: 'link',
      },
    }))}
    columns={2}
  />

  <!-- 服务时间和说明 -->
  <Content
    title="服务说明"
    subtitle="了解我们的服务时间和响应政策"
    items={[
      {
        title: '响应时间',
        description: '工作日内24小时回复邮件，QQ和微信消息通常在2小时内回复。',
        icon: 'tabler:clock',
      },
      {
        title: '服务范围',
        description: '技术支持、软件问题、商务合作、远程刷机服务等。',
        icon: 'tabler:tools',
      },
      {
        title: '工作时间',
        description: '周一至周日 9:00-22:00，节假日可能延迟回复。',
        icon: 'tabler:calendar',
      },
    ]}
  >
    <Fragment slot="content">
      <h3 class="text-2xl font-bold tracking-tight dark:text-white sm:text-3xl mb-2">
        专业的技术支持团队
      </h3>
      我们拥有经验丰富的技术团队，为您提供专业、及时的技术支持和服务。
    </Fragment>

    <Fragment slot="bg">
      <div class="absolute inset-0 bg-blue-50 dark:bg-slate-800"></div>
    </Fragment>
  </Content>

  <!-- Call to Action -->
  <CallToAction
    actions={[
      {
        variant: 'primary',
        text: '返回首页',
        href: '/',
        icon: 'tabler:home',
      },
      {
        variant: 'secondary',
        text: '查看项目',
        href: '/#projects',
        icon: 'tabler:apps',
      },
    ]}
  >
    <Fragment slot="title">
      还有其他问题？
    </Fragment>

    <Fragment slot="subtitle">
      欢迎浏览我们的项目展示，或返回首页了解更多信息。
    </Fragment>
  </CallToAction>
</Layout>
