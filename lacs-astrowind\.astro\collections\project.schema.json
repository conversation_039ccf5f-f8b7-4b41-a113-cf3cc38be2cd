{"$ref": "#/definitions/project", "definitions": {"project": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number"}, "category": {"type": "string"}, "categoryName": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "platform": {"type": "string"}, "updateDate": {"type": "string"}, "link": {"type": "string"}, "icon": {"type": "string"}}, "required": ["id", "category", "categoryName", "title", "description", "platform", "updateDate", "link", "icon"], "additionalProperties": false}}}, "$schema": "http://json-schema.org/draft-07/schema#"}