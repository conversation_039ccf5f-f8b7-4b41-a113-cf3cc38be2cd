<!DOCTYPE html>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>随机选择</title>
<style>
body {
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
    background: #f0f4f8;
    text-align: center;
    padding: 0;
    margin: 0;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    color: #2c3e50;
}

/* 简化动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.container-background {
    flex: 1;
    overflow: auto;
    position: relative;
    margin: 15px;
    padding: 20px;
    border-radius: 12px;
    background: #ffffff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.container, .container-big {
    background-color: #f9f9f9;
    border-radius: 10px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 90%;
    margin: 0;
    max-width: 800px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.container-big {
    height: auto;
    min-height: 150px;
    max-height: 200px;
    overflow-y: auto;
}

button {
    background: #4a90e2;
    color: white;
    padding: 12px 24px;
    margin: 10px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    min-height: 45px;
    min-width: 180px;
    pointer-events: none;
    opacity: 0.6;
    transition: background-color 0.2s, opacity 0.2s;
}

button.enabled {
    pointer-events: auto;
    opacity: 1;
}

button:hover {
    background: #3a80d2;
}

p {
    font-size: 16px;
    margin-top: 12px;
    color: #2c3e50;
    line-height: 1.5;
}

h1 {
    color: #4a90e2;
    margin-bottom: 15px;
    font-size: 24px;
    font-weight: 500;
}

.button-container {
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* 公告样式优化 */
#selectionResult-a {
    font-weight: bold;
    color: #2575fc;
}

#adContentText {
    color: #333;
    font-weight: normal;
    padding-left: 8px;
}

/* 结果显示优化 */
#selectionResult {
    font-size: 20px;
    font-weight: bold;
    color: #4a90e2;
    padding: 10px;
    border-radius: 6px;
    background-color: #f0f4f8;
    opacity: 0;
    transition: opacity 0.3s ease;
}

#selectionResult.show {
    opacity: 1;
}

/* 页脚样式优化 */
footer {
    padding: 10px;
    background-color: #f0f4f8;
    margin-top: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

footer a {
    color: #2575fc;
    text-decoration: none;
    transition: color 0.3s ease;
}

footer a:hover {
    color: #6a11cb;
    text-decoration: underline;
}

@media screen and (max-width: 600px) {
    .container-background {
        width: 100%;
        margin: 0;
        padding: 5px;
    }

    .container, .container-big {
        width: 95%;
        margin: 8px auto;
        padding: 15px;
        max-width: 100%;
    }
    
    h1 {
        font-size: 20px;
    }

    button {
        width: 100%;
        padding: 10px 20px;
        font-size: 16px;
        margin-bottom: 10px;
    }
    
    #floorSelect {
        width: 100%;
        padding: 8px;
        font-size: 14px;
        margin-bottom: 15px;
    }
    
    p {
        font-size: 14px;
    }
    
    #selectionResult {
        font-size: 18px;
        padding: 8px;
    }
    
    footer {
        flex-direction: column;
        gap: 8px;
        padding: 8px;
    }
}

/* 平板设备优化 */
@media screen and (min-width: 601px) and (max-width: 1024px) {
    .container, .container-big {
        width: 90%;
    }
    
    button {
        padding: 10px 20px;
    }
}

#floorSelect {
    padding: 10px;
    border-radius: 6px;
    border: 1px solid #4a90e2;
    background-color: #ffffff;
    font-size: 14px;
    color: #2c3e50;
    cursor: pointer;
    margin-bottom: 15px;
    min-width: 150px;
}

#floorSelect:focus {
    outline: none;
    border-color: #4a90e2;
}

.settings-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 9999;
    display: none;

}

.settings-content {
    background-color: #ffffff;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 85%;
    max-width: 600px;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    align-items: center;
}

.settings-header {
    width: 100%;
    text-align: center;
    margin-bottom: 15px;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.settings-body {
    width: 100%;
    text-align: left;
}

.settings-body h2 {
    color: #2575fc;
    margin-top: 15px;
}

.settings-body p {
    font-size: 16px;
    line-height: 1.6;
}
</style>

</head>
<body>
<div class="container-background">
    <div class="container">
        <span>
            <p id="selectionResult-a" style="display: inline-block; margin: 0;">公告:</p>
            <p id="adContentText" style="display: inline-block; margin: 0;">test</p>
        </span>
    </div>
    <div class="container">
        <h1>今天吃/喝什么？</h1>
        <div class="button-container">
            <div>
                <label for="floorSelect" style="font-weight: bold; font-size: 18px;">楼层选择:</label>
                <select id="floorSelect" onchange="handleFloorSelect(this.value)">
                    <option value="random">随机</option>
                    <option value="2">2层</option>
                    <option value="3">3层</option>
                </select>
            </div>

            <div style="width: 100%; display: flex; flex-direction: column; align-items: center;">
                <button id="storeButton" onclick="selectRandomStore()" data-umami-event="随机店家">随机店家</button>
                <button id="cafeButton" onclick="selectWater()" data-umami-event="随机饮品">随机饮品</button>
            </div>
        </div>
    </div>
    <div class="container">
        <p id="selectionResult"></p>
    </div>
    <div class="container">
        <h1>日志信息</h1>
        <p id="log_text">正在获取数据...</p>
    </div>
</div>

<div class="settings-container">
    <div class="settings-content">
        <a href="#" onclick="settings()" style="position: absolute; top: 15px; right: 15px; font-size: 20px; color: #2575fc;">✕</a>
        <div class="settings-header">
            <h1>关于本站</h1>
        </div>
        <div class="settings-body">
            <h2>网站整体逻辑</h2>
            <p>本站的主要逻辑是随机选择一个楼层的店家和饮品，并展示给用户。用户可以选择楼层，也可以随机选择。本站的主要数据来源是通过接口获取的，接口地址为https://api.xn--5brr03o.top/test/test.json，数据格式为json。</p>
            <p>用户操作的流程是：选择楼层，点击随机店家或随机饮品按钮，页面展示随机选择的店家或饮品。</p>
        </div>
    </div>
</div>

<footer>
    <span><p style="margin: 0; font-size: 16px;">© 2025 <a href="https://www.xn--5brr03o.top/">LAC Studio</a> and <a href="https://blog.ssxaya.fun/">ssxaya</a></p></span>
    <a href="#" onclick="settings()" style="font-size: 16px;">关于本站</a>
</footer>

<script defer src="https://umami.xn--5brr03o.top/script.js" data-website-id="a4e8c20f-d2e8-4b10-bdf5-2d52c389fd45"></script>
<script src="../js/main.js"></script>
<script>
    let data = {
        2: { stores: [], waters: [] },
        3: { stores: [], waters: [] },
        notice: 'test'
    };

    let selectedFloor = 'random';
    let isLoading = true;

    // 通用数据加载函数
    async function fetchData() {
        try {
            const response = await fetch('https://api.xn--5brr03o.top/test/test.json');
            if (!response.ok) throw new Error('网络响应不正常: ' + response.statusText);
            
            // 获取原始文本以便调试
            const rawText = await response.text();
            
            try {
                // 尝试解析 JSON
                return JSON.parse(rawText);
            } catch (parseError) {
                // 提供更详细的 JSON 解析错误信息
                console.error('JSON 解析错误:', parseError);
                console.error('原始响应:', rawText);
                throw new Error(`JSON 解析错误: ${parseError.message}。请检查 API 返回的数据格式。`);
            }
        } catch (error) {
            throw error;
        }
    }

    // 处理数据加载错误
    function handleDataError(error) {
        console.error('获取数据时出错:', error);
        isLoading = false;
        document.getElementById('storeButton').classList.add('enabled');
        document.getElementById('cafeButton').classList.add('enabled');
        document.getElementById('log_text').innerText = '获取数据时出错: ' + error.message;
        document.getElementById('adContentText').innerText = '数据加载失败';
        
        // 添加默认数据以便网站仍然可用
        data = {
            2: { 
                stores: ['默认餐厅1', '默认餐厅2', '默认餐厅3'], 
                waters: ['默认饮品1', '默认饮品2', '默认饮品3'] 
            },
            3: { 
                stores: ['默认餐厅4', '默认餐厅5', '默认餐厅6'], 
                waters: ['默认饮品4', '默认饮品5', '默认饮品6'] 
            },
            notice: '数据加载失败，使用默认数据'
        };
    }

    // 更新数据并启用按钮
    function updateDataAndUI({second, third, notice}) {
        data['2'].stores = second?.stores || [];
        data['2'].waters = second?.waters || [];
        data['3'].stores = third?.stores || [];
        data['3'].waters = third?.waters || [];
        data.notice = notice || 'test';
        isLoading = false;

        // 更新 UI
        document.getElementById('storeButton').classList.add('enabled');
        document.getElementById('cafeButton').classList.add('enabled');
        document.getElementById('log_text').innerText = '商家数据已更新 饮品数据已更新';
        document.getElementById('adContentText').innerText = data.notice;
    }

    // 页面加载时调用
    window.onload = async () => {
        try {
            const apiData = await fetchData();
            updateDataAndUI(apiData);
        } catch (error) {
            handleDataError(error);
        }
    };

    // 楼层选择处理
    function handleFloorSelect(value) {
        selectedFloor = value;
    }

    // 通用随机选择函数
    function getRandomItem(items, loadingMessage) {
        if (isLoading) return loadingMessage;
        if (items.length === 0) return "数据加载中...";
        return items[Math.floor(Math.random() * items.length)];
    }

    // 随机选择店家
    function selectRandomStore() {
        const floors = selectedFloor === 'random' ? ['2', '3'] : [selectedFloor];
        const floor = floors[Math.floor(Math.random() * floors.length)];
        const result = getRandomItem(data[floor].stores, "商家数据加载中...");
        const selectionResult = document.getElementById("selectionResult");
        selectionResult.innerText = result;
        selectionResult.classList.add('show');
    }

    // 随机选择饮品
    function selectWater() {
        const floors = selectedFloor === 'random' ? ['2', '3'] : [selectedFloor];
        const floor = floors[Math.floor(Math.random() * floors.length)];
        const result = getRandomItem(data[floor].waters, "饮品数据加载中...");
        const selectionResult = document.getElementById("selectionResult");
        selectionResult.innerText = result;
        selectionResult.classList.add('show');
    }

    function settings() {
        if (document.getElementsByClassName("settings-container")[0].style.display === "none") {
            document.getElementsByClassName("settings-container")[0].style.display = "block";
        } else {
            document.getElementsByClassName("settings-container")[0].style.display = "none";
        }
    }
</script>
</body>
</html>