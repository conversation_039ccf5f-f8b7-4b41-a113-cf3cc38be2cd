backend:
  name: git-gateway
  branch: main

media_folder: 'src/assets/images'
public_folder: '/_astro'

collections:
  - name: 'post'
    label: 'Post'
    folder: 'src/content/post'
    create: true
    fields:
      - { label: 'Title', name: 'title', widget: 'string' }
      - { label: 'Excerpt', name: 'excerpt', widget: 'string' }
      - { label: 'Category', name: 'category', widget: 'string' }
      - {
          label: 'Tags',
          name: 'tags',
          widget: 'list',
          allow_add: true,
          allow_delete: true,
          collapsed: false,
          field: { label: 'Tag', name: 'tag', widget: 'string' },
        }
      - { label: 'Image', name: 'image', widget: 'string' }
      - { label: 'Publish Date', name: 'publishDate', widget: 'datetime', required: false }
      - { label: 'Author', name: 'author', widget: 'string' }
      - { label: 'Content', name: 'body', widget: 'markdown' }
