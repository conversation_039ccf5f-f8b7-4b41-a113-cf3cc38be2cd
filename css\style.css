/* 加载指示器样式 */
.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: 50px;
}

/* 使用更现代的旋转动画和颜色 */
.fas.fa-circle-notch.fa-spin {
    animation: rotate 1s linear infinite;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 提示文本样式 */
.loading p {
    color: #6b7280; /* 使用更柔和的文字颜色 */
    font-size: 1.2rem; /* 增大字体大小以便更清晰地显示 */
    margin-top: 20px; /* 增加与加载指示器的间距 */
}
