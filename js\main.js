// 移动端菜单切换
document.getElementById('menu-toggle').addEventListener('click', function() {
    const mobileMenu = document.getElementById('mobile-menu');
    mobileMenu.classList.toggle('hidden');
});

// 平滑滚动和锚点导航
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        
        const targetId = this.getAttribute('href');
        if (targetId === '#') return;

        const targetElement = document.querySelector(targetId);
        if (targetElement) {
            window.scrollTo({
                top: targetElement.offsetTop - 80,
                behavior: 'smooth'
            });

            // 移动端点击后关闭菜单
            const mobileMenu = document.getElementById('mobile-menu');
            if (!mobileMenu.classList.contains('hidden')) {
                mobileMenu.classList.add('hidden');
            }
        }
    });
});

// 返回顶部按钮和导航栏滚动效果
const backToTopButton = document.getElementById('back-to-top');
const nav = document.getElementById('main-nav');

window.addEventListener('scroll', () => {
    // 返回顶部按钮显示/隐藏
    backToTopButton.classList.toggle('opacity-0', window.scrollY <= 300);
    backToTopButton.classList.toggle('invisible', window.scrollY <= 300);
    backToTopButton.classList.toggle('opacity-100', window.scrollY > 300);
    backToTopButton.classList.toggle('visible', window.scrollY > 300);

    // 导航栏滚动效果
    nav.classList.toggle('nav-scrolled', window.scrollY > 50);
    nav.classList.toggle('nav-default', window.scrollY <= 50);
});

backToTopButton.addEventListener('click', () => {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
});

// 模态框功能
const modal = document.getElementById('modal');
const modalContent = document.getElementById('modal-content');
const modalImage = document.getElementById('modal-image');
const modalTitle = document.getElementById('modal-title');
const closeModal = document.getElementById('close-modal');

function showModal(imageUrl, title = '', link = '') {
    modalImage.src = imageUrl;
    modalTitle.textContent = title || '详情';

    modal.classList.remove('hidden');
    setTimeout(() => {
        modalContent.classList.remove('scale-95', 'opacity-0');
        modalContent.classList.add('scale-100', 'opacity-100');
    }, 10);
    
    // 禁止背景滚动
    document.body.style.overflow = 'hidden';
}

function closeModalFunc() {
    modalContent.classList.remove('scale-100', 'opacity-100');
    modalContent.classList.add('scale-95', 'opacity-0');
    setTimeout(() => {
        modal.classList.add('hidden');
        // 恢复背景滚动
        document.body.style.overflow = '';
    }, 300);
}

closeModal.addEventListener('click', closeModalFunc);
modal.addEventListener('click', (e) => {
    if (e.target === modal) {
        closeModalFunc();
    }
});

// 主题切换功能
const darkModeToggle = document.getElementById('dark-mode-toggle');

function toggleDarkMode() {
    document.body.classList.toggle('dark');
    // 更新图标
    const isDark = document.body.classList.contains('dark');
    darkModeToggle.innerHTML = isDark ? '<i class="fa fa-sun"></i>' : '<i class="fa fa-moon-o"></i>';
}

darkModeToggle.addEventListener('click', toggleDarkMode);

// 项目筛选功能
document.addEventListener('DOMContentLoaded', async () => {
    // 初始化页面
    try {
        // 并行加载项目和分站数据
        await Promise.all([loadProjectsData(), loadSitesData()]);
        
        // 生成项目和分站内容
        generateProjectCategoryButtons();
        generateProjectCards(projectsData);
        generateSitesCards();
        
        // 添加浮动按钮动画
        const floatingButtons = document.querySelector('.fixed.bottom-6');
        if (floatingButtons) {
            floatingButtons.classList.add('animate-bounce-highlight');
            
            // 3秒后移除动画效果
            setTimeout(() => {
                floatingButtons.classList.remove('animate-bounce-highlight');
            }, 3000);
        }
    } catch (error) {
        console.error('初始化页面失败:', error);
    }

});


// 打开链接函数
function openLink(url) {
    window.open(url, '_blank');
}

// 获取DOM元素
const projectButtonsContainer = document.getElementById('project-buttons');
const projectCardsContainer = document.getElementById('project-cards-container');
const sitesContainer = document.getElementById('sites-container');
let projectsData = [];
let sitesData = [];

// 加载JSON数据
async function loadJsonData(url) {
    try {
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return await response.json();
    } catch (error) {
        console.error('加载数据失败:', error);
        throw error;
    }
}

// 加载项目数据
async function loadProjectsData() {
    try {
        projectsData = await loadJsonData('json/projects_list.json');
        return projectsData;
    } catch (error) {
        if (projectCardsContainer) {
            projectCardsContainer.innerHTML = `
                <div class="loading">
                    <i class="fas fa-exclamation-circle fa-2x mb-2 text-red-500"></i>
                    <p>加载项目数据失败，请检查网络连接</p>
                </div>
            `;
        }
        throw error;
    }
}

// 加载分站数据
async function loadSitesData() {
    try {
        sitesData = await loadJsonData('json/sites_list.json');
        return sitesData;
    } catch (error) {
        if (sitesContainer) {
            sitesContainer.innerHTML = `
                <div class="loading">
                    <i class="fas fa-exclamation-circle fa-2x mb-2 text-red-500"></i>
                    <p>加载分站数据失败，请检查网络连接</p>
                </div>
            `;
        }
        throw error;
    }
}

// 生成项目分类按钮
function generateProjectCategoryButtons() {
    if (!projectButtonsContainer) return;
    
    projectButtonsContainer.innerHTML = ''; // 清空容器
    
    // 获取唯一的分类
    const categories = [...new Set(projectsData.map(project => project.category))];
    
    // 创建"全部项目"按钮
    const allButton = document.createElement('button');
    allButton.type = 'button';
    allButton.className = 'btn-filter active bg-primary/90 text-white backdrop-blur-sm rounded-full px-4 py-2 transition-all duration-300 hover:shadow-neon-primary flex items-center';
    allButton.dataset.project = 'all';
    allButton.innerHTML = '<i class="fas fa-layer-group mr-2"></i>全部项目';
    allButton.addEventListener('click', () => filterProjects('all'));
    projectButtonsContainer.appendChild(allButton);
    
    // 创建分类按钮
    categories.forEach(category => {
        const categoryName = projectsData.find(p => p.category === category)?.categoryName;
        if (categoryName) {
            const button = document.createElement('button');
            button.type = 'button';
            button.className = 'btn-filter bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm text-gray-700 dark:text-gray-300 rounded-full px-4 py-2 transition-all duration-300 hover:bg-primary/90 hover:text-white hover:shadow-neon-primary/50 flex items-center';
            button.dataset.project = category;
            button.innerHTML = '<i class="fas fa-tag mr-2"></i>' + categoryName;
            button.addEventListener('click', () => filterProjects(category));
            projectButtonsContainer.appendChild(button);
        }
    });
}

// 生成项目卡片
function generateProjectCards(projects) {
    if (!projectCardsContainer) return;
    
    projectCardsContainer.innerHTML = ''; // 清空容器
    
    if (projects.length === 0) {
        projectCardsContainer.innerHTML = `
            <div class="loading">
                <i class="fas fa-search fa-2x mb-2 text-gray-400"></i>
                <p>没有找到匹配的项目</p>
            </div>
        `;
        return;
    }
    
    projects.forEach(project => {
        const card = document.createElement('div');
        card.className = 'project-card bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-xl tech-border border border-gray-200 dark:border-gray-700 overflow-hidden transition-all duration-300 hover:shadow-neon-primary dark:hover:shadow-neon-accent transform hover:-translate-y-1 hover:scale-[1.02] group w-full';
        card.dataset.category = project.category;
        
        card.innerHTML = `
            <div class="p-6">
                <div class="card-icon-wrapper mb-4 w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                    <i class="fa ${project.icon} fa-xl text-primary"></i>
                </div>
                <h5 class="card-title text-xl font-bold mb-2 bg-gradient-text">${project.title}</h5>
                <p class="card-text text-gray-600 dark:text-gray-300 mb-4">${project.description}</p>
                <div class="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-4">
                    <span class="flex items-center mr-3"><i class="fa fa-code mr-1"></i> ${project.platform}</span>
                    ${project.updateDate ? 
            `<span class="flex items-center"><i class="fa fa-calendar-alt mr-1"></i> 更新于 ${project.updateDate}</span>` : 
            ''}
                </div>
            </div>
            <div class="px-6 pb-6">
                <a href="${project.link}" class="btn-primary block text-center transition-all duration-300 hover:shadow-neon-primary group-hover:animate-pulse-slow" target="_blank" rel="noopener noreferrer" data-umami-event="${project.title}">
                    <i class="fas fa-external-link-alt mr-1"></i> 立即访问
                </a>
            </div>
        `;
        
        projectCardsContainer.appendChild(card);
    });
}

// 筛选项目
function filterProjects(category) {
    // 更新按钮样式
    const buttons = document.querySelectorAll('.btn-filter');
    buttons.forEach(btn => {
        if (btn.dataset.project === category) {
            btn.classList.add('active', 'bg-primary/90', 'text-white', 'shadow-neon-primary/50');
            btn.classList.remove('bg-white/80', 'dark:bg-gray-800/80', 'text-gray-700', 'dark:text-gray-300', 'hover:bg-primary/90', 'hover:text-white');
        } else {
            btn.classList.remove('active', 'bg-primary/90', 'text-white', 'shadow-neon-primary/50');
            btn.classList.add('bg-white/80', 'dark:bg-gray-800/80', 'text-gray-700', 'dark:text-gray-300', 'hover:bg-primary/90', 'hover:text-white');
        }
    });
    
    // 筛选项目
    let filteredProjects;
    if (category === 'all') {
        filteredProjects = projectsData;
    } else {
        filteredProjects = projectsData.filter(project => project.category === category);
    }
    
    // 生成筛选后的项目卡片，添加动画效果
    projectCardsContainer.classList.add('animate__animated', 'animate__fadeIn');
    generateProjectCards(filteredProjects);
    setTimeout(() => {
        projectCardsContainer.classList.remove('animate__animated', 'animate__fadeIn');
    }, 500);
}

// 生成分站卡片
function generateSitesCards() {
    if (!sitesContainer) return;
    
    sitesContainer.innerHTML = ''; // 清空容器
    
    if (sitesData.length === 0) {
        sitesContainer.innerHTML = `
            <div class="loading">
                <i class="fas fa-search fa-2x mb-2 text-gray-400"></i>
                <p>没有找到分站数据</p>
            </div>
        `;
        return;
    }
    
    sitesData.forEach(site => {
        const card = document.createElement('div');
        card.className = 'site-card bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg tech-border border border-gray-200 dark:border-gray-700 overflow-hidden transition-all duration-300 hover:shadow-neon-secondary dark:hover:shadow-neon-primary transform hover:-translate-y-1 hover:scale-[1.02] group';
        // 根据图标类型生成不同的图标内容
        iconContent = `<img src="${site.iconContent}" class="w-8 h-8" alt="${site.name}">`;
        
        card.innerHTML = `
            <div class="relative overflow-hidden">
                <img src="${site.logo || 'img/placeholder.png'}" alt="${site.name}" class="w-full h-52 object-cover transition-transform duration-500 group-hover:scale-110">
                <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                ${site.status ? `<div class="absolute top-2 right-2 bg-secondary/90 backdrop-blur-sm text-white text-xs px-3 py-1 rounded-full shadow-neon-secondary/30 border border-secondary/20 flex items-center"><span class="w-1.5 h-1.5 bg-white rounded-full mr-1 animate-pulse"></span>${site.status}</div>` : ''}
            </div>
            <div class="p-5 relative">
                <!-- 装饰性线条 -->
                <div class="absolute top-0 left-0 w-16 h-1 bg-gradient-to-r from-secondary to-transparent rounded-full"></div>
                <div class="absolute top-0 right-0 w-16 h-1 bg-gradient-to-l from-primary to-transparent rounded-full"></div>
                
                <h3 class="text-lg font-bold mb-2 bg-gradient-text">${site.name}</h3>
                <p class="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-1">${site.description}</p>
                <div class="flex justify-between items-center">
                    <span class="text-xs text-gray-500 dark:text-gray-400 flex items-center"><i class="fas fa-tag mr-1"></i>${site.category || ''}</span>
                    <a href="${site.link}" target="_blank" rel="noopener noreferrer" class="btn-sm bg-secondary/90 backdrop-blur-sm text-white rounded-full px-4 py-1.5 transition-all duration-300 hover:shadow-neon-secondary flex items-center group-hover:animate-pulse-slow" data-umami-event="${site.analyticsEvent}">
                        <i class="fas fa-external-link-alt mr-1"></i>
                        <span>访问站点</span>
                    </a>
                </div>
            </div>
        `;

        sitesContainer.appendChild(card);
    });
}


// 生成媒体平台卡片
async function generatePlatformsCards() {
    const container = document.getElementById('platforms-cards-container');
    if (!container) return;

    // 清空容器并显示加载状态
    container.innerHTML = `
                <div class="loading col-span-full flex flex-col items-center justify-center py-12">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
                    <p class="mt-4 text-gray-500 dark:text-gray-400">加载中...</p>
                </div>
            `;

    try {
        const response = await fetch('json/mediaPlatforms.json');
        const platformsData = await response.json();

        if (!platformsData || platformsData.length === 0) {
            container.innerHTML = `
                        <div class="col-span-full flex flex-col items-center justify-center py-12">
                            <i class="fas fa-search fa-2x mb-2 text-gray-400"></i>
                            <p class="text-gray-500 dark:text-gray-400">没有找到媒体平台数据</p>
                        </div>
                    `;
            return;
        }

        // 清空加载状态
        container.innerHTML = '';

        // 生成每个媒体平台卡片
        platformsData.forEach(platform => {
            const card = document.createElement('div');
            card.className = 'bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-lg p-4 hover:shadow-md transition-all duration-300 border border-gray-200 dark:border-gray-700 hover:border-primary-300 dark:hover:border-primary-500 group/card';

            // 上半部分: logo 和平台名称
            let cardContent = `
                        <div class="flex items-center mb-3">
                            <img src="${platform.logo}" class="h-10 w-10 mr-3 rounded-full border-2 border-transparent group-hover/card:border-primary-200 transition-all duration-300" alt="${platform.name}">
                            <h6 class="font-bold group-hover/card:text-primary-600 transition-colors duration-300">${platform.name}</h6>
                        </div>
                    `;

            // 中间部分: 用户名和 UID
            cardContent += `
                        <div class="mb-3">
                            <span class="mr-2">@${platform.account}</span>
                            ${platform.accountId ? `<p>UID: ${platform.accountId}</p>` : ''}
                        </div>
                    `;

            // 下半部分: 二维码按钮与访问链接按钮
            cardContent += `
                        <div class="flex justify-between items-center">
                            <div class="flex space-x-2">
                                <button class="tech-button-sm text-primary-600 hover:text-primary-800 bg-primary-50 hover:bg-primary-100 dark:bg-gray-700 dark:hover:bg-gray-600 transition-colors duration-300 p-2 rounded-lg" onclick="showModal('${platform.qrcode}', '${platform.name}')" aria-label="查看${platform.name}二维码">
                                    <i class="fas fa-qrcode"></i>
                                </button>
                                <button class="tech-button-sm text-primary-600 hover:text-primary-800 bg-primary-50 hover:bg-primary-100 dark:bg-gray-700 dark:hover:bg-gray-600 transition-colors duration-300 p-2 rounded-lg" onclick="openLink('${platform.link}')" data-umami-event="${platform.analyticsEvent || ''}" aria-label="访问${platform.name}">
                                    <i class="fas fa-external-link-alt"></i>
                                    <span class="ml-1 inline">访问</span>
                                </button>
                            </div>
                        </div>
                    `;

            card.innerHTML = cardContent;
            container.appendChild(card);
        });
    } catch (error) {
        console.error('加载媒体平台数据失败:', error);
        container.innerHTML = `
                    <div class="col-span-full flex flex-col items-center justify-center py-12">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2 text-red-500"></i>
                        <p class="text-red-500 dark:text-red-400">加载媒体平台数据失败，请稍后再试</p>
                        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">错误详情: ${error.message}</p>
                    </div>
                `;
    }
}


// 生成联系方式卡片
async function generateContactCards() {
    const contactContainer = document.getElementById('contact-container');
    if (!contactContainer) return;

    contactContainer.innerHTML = ''; // 清空容器

    try {
        const response = await fetch('json/contactInfo.json');
        const contactData = await response.json();

        contactData.forEach(contact => {
            const card = document.createElement('div');
            card.className = 'bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-lg p-4 hover:shadow-md transition-all duration-300 border border-gray-200 dark:border-gray-700 hover:border-primary-300 dark:hover:border-primary-500 group/card';
            
            card.innerHTML = `
                <div class="flex items-center mb-3">
                    <img src="${contact.logo}" class="h-10 w-10 mr-3 rounded-full border-2 border-transparent group-hover/card:border-primary-200 transition-all duration-300" alt="${contact.title}">
                    <h6 class="font-bold group-hover/card:text-primary-600 transition-colors duration-300">${contact.title}</h6>
                </div>
                <div class="mb-3">
                    <p class="text-sm text-gray-600 mb-2">${contact.description}</p>
                    <span class="font-mono text-sm">${contact.info}</span>
                </div>
                <div class="flex justify-end">
                    <button class="tech-button-sm text-primary-600 hover:text-primary-800 bg-primary-50 hover:bg-primary-100 dark:bg-gray-700 dark:hover:bg-gray-600 transition-colors duration-300 p-2 rounded-lg" onclick="openLink('${contact.action}')" data-umami-event="${contact.analyticsEvent}" aria-label="联系${contact.title}">
                        <i class="fas fa-external-link-alt"></i>
                        <span class="ml-1 inline">联系</span>
                    </button>
                </div>
            `;

            contactContainer.appendChild(card);
        });
    } catch (error) {
        console.error('加载联系方式数据失败:', error);
        contactContainer.innerHTML = `
            <div class="loading">
                <i class="fas fa-exclamation-triangle fa-2x mb-2 text-red-500"></i>
                <p>加载联系方式数据失败，请稍后再试</p>
            </div>
        `;
    }
}

// 生成群聊卡片
async function generateGroupChatCards() {
    const groupChatContainer = document.getElementById('group-chat-container');
    if (!groupChatContainer) return;

    groupChatContainer.innerHTML = ''; // 清空容器

    try {
        const response = await fetch('json/groupChats.json');
        const groupChatData = await response.json();

        groupChatData.forEach(groupChat => {
            const card = document.createElement('div');
            // 修改为与媒体平台卡片相似的样式
            card.className = 'bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-lg p-4 hover:shadow-md transition-all duration-300 border border-gray-200 dark:border-gray-700 hover:border-secondary-300 dark:hover:border-secondary-500 group/card';
            
            card.innerHTML = `
                <div class="flex items-center mb-3">
                    <img src="${groupChat.logo}" class="h-10 w-10 mr-3 rounded-full border-2 border-transparent group-hover/card:border-secondary-200 transition-all duration-300" alt="${groupChat.name}">
                    <div>
                        <h6 class="font-bold group-hover/card:text-secondary-600 transition-colors duration-300">${groupChat.name}</h6>
                        <p class="text-sm text-gray-500">${groupChat.limit || groupChat.channelId}</p>
                    </div>
                </div>
                <div class="mb-3">
                    <span class="mr-2">群号${groupChat.groupNumber || ''}</span>
                </div>
                <div class="flex justify-between items-center">
                    <div class="flex space-x-2">
                        <button class="tech-button-sm text-secondary-600 hover:text-secondary-800 bg-secondary-50 hover:bg-secondary-100 dark:bg-gray-700 dark:hover:bg-gray-600 transition-colors duration-300 p-2 rounded-lg" onclick="showModal('${groupChat.qrcode}', '${groupChat.name}')" aria-label="查看${groupChat.name}二维码">
                            <i class="fas fa-qrcode"></i>
                        </button>
                        <button class="tech-button-sm text-secondary-600 hover:text-secondary-800 bg-secondary-50 hover:bg-secondary-100 dark:bg-gray-700 dark:hover:bg-gray-600 transition-colors duration-300 p-2 rounded-lg" onclick="openLink('${groupChat.joinLink}')" data-umami-event="${groupChat.analyticsEvent}">
                            <i class="fas fa-user-plus"></i>
                            <span class="ml-1 inline">加入</span>
                        </button>
                    </div>
                </div>
            `;

            groupChatContainer.appendChild(card);
        });
    } catch (error) {
        console.error('加载群聊数据失败:', error);
        groupChatContainer.innerHTML = `
            <div class="loading">
                <i class="fas fa-exclamation-triangle fa-2x mb-2 text-red-500"></i>
                <p>加载群聊数据失败，请稍后再试</p>
            </div>
        `;
    }
}

// 打开链接函数
function openLink(url) {
    window.open(url, '_blank');
}

// 页面加载完成后调用函数
document.addEventListener('DOMContentLoaded', async () => {
    await generatePlatformsCards();
    await generateContactCards();
    await generateGroupChatCards();
});





// 友情链接申请弹窗控制
const applyLinkBtn = document.getElementById('apply-link-btn');
const linkModal = document.getElementById('link-modal');
const linkModalContent = document.getElementById('link-modal-content');
const closeLinkModal = document.getElementById('close-link-modal');

// 打开弹窗
applyLinkBtn.addEventListener('click', () => {
    linkModal.classList.remove('hidden');
    // 触发动画
    setTimeout(() => {
        linkModalContent.classList.remove('scale-95', 'opacity-0');
        linkModalContent.classList.add('scale-100', 'opacity-100');
    }, 10);
    // 阻止页面滚动
    document.body.style.overflow = 'hidden';
});

// 关闭弹窗
function closeLinkModalFunc() {
    linkModalContent.classList.remove('scale-100', 'opacity-100');
    linkModalContent.classList.add('scale-95', 'opacity-0');
    setTimeout(() => {
        linkModal.classList.add('hidden');
        // 恢复页面滚动
        document.body.style.overflow = '';
    }, 300);
}

closeLinkModal.addEventListener('click', closeLinkModalFunc);

// 点击弹窗外部关闭
linkModal.addEventListener('click', (e) => {
    if (e.target === linkModal) {
        closeLinkModalFunc();
    }
});

// 按ESC键关闭
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && !linkModal.classList.contains('hidden')) {
        closeLinkModalFunc();
    }
});