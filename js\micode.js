// 设备数据和解锁方法映射
const UNLOCK_METHODS = {
official: [
    "1. 确保您的设备已开启USB调试模式（设置 > 关于手机 > 软件信息 > 多次点击版本号开启开发者选项，然后在开发者选项中开启USB调试）",
    "2. 从小米官网下载并安装最新版的MiFlashUnlock工具",
    "3. 使用USB数据线将手机连接到电脑",
    "4. 打开MiFlashUnlock工具，按照提示进行操作",
    "5. 等待解锁完成，这可能需要一些时间"
],
hout: [
    "1. 下载并安装澎湃解锁工具箱到您的电脑",
    "2. 备份您手机中的所有重要数据",
    "3. 进入Fastboot模式（通常是关机后同时按住电源键和音量下键）",
    "4. 使用USB数据线将手机连接到电脑",
    "5. 打开澎湃解锁工具箱，选择对应的设备型号和解锁选项",
    "6. 点击开始解锁并等待过程完成"
],
answer: [
    "1. 访问小米社区网站并登录您的账号",
    "2. 进入解锁专区，完成答题环节",
    "3. 答题通过后，申请解锁权限",
    "4. 等待审核通过，通常需要1-7天",
    "5. 审核通过后，下载解锁工具并按照指引完成解锁"
],
"9008": [
    "1. 确保您的设备已进入9008模式（通常需要短接特定触点）",
    "2. 下载并安装QPST工具和对应设备的驱动",
    "3. 使用USB数据线将设备连接到电脑",
    "4. 打开QPST工具，选择合适的刷机包",
    "5. 开始刷机过程，这将清除所有数据并恢复到指定系统",
    "6. 刷机完成后，设备将自动重启"
],
mtk: [
    "1. 下载并安装联发科解锁工具",
    "2. 备份您手机中的所有重要数据",
    "3. 关机后，按住特定按键组合进入特殊模式（通常是音量上下键+电源键）",
    "4. 使用USB数据线将手机连接到电脑",
    "5. 打开联发科解锁工具，点击开始解锁",
    "6. 等待工具完成解锁过程，这通常只需要几秒钟"
]
};

// 解锁类型与UI显示映射
const UNLOCK_TYPE_UI = {
official: { text: '官方解锁工具', badgeClass: 'badge-success', icon: 'fa-check-circle' },
hout: { text: '澎湃解锁工具箱', badgeClass: 'badge-warning', icon: 'fa-wrench' },
answer: { text: '社区答题解锁', badgeClass: 'badge-info', icon: 'fa-question-circle' },
"9008": { text: '9008解锁', badgeClass: 'badge-purple', icon: 'fa-cogs' },
mtk: { text: '联发科解锁', badgeClass: 'badge-danger', icon: 'fa-bolt' }
};

// DOM元素
const loading = document.getElementById('loading');
const error = document.getElementById('error');
const content = document.getElementById('content');
const devicesGrid = document.getElementById('devices-grid');
const emptyState = document.getElementById('empty-state');
const searchInput = document.getElementById('search-input');
const clearSearch = document.getElementById('clear-search');
const deviceCount = document.getElementById('device-count');
const retryBtn = document.getElementById('retry-btn');
const refreshBtn = document.getElementById('refresh-btn');
const filterToggle = document.getElementById('filter-toggle');
const mobileFilter = document.getElementById('mobile-filter');
const mobileFilterPanel = document.getElementById('mobile-filter-panel');
const closeFilter = document.getElementById('close-filter');
const applyFilter = document.getElementById('apply-filter');
const unlockFilter = document.getElementById('unlock-filter');
const dateFilter = document.getElementById('date-filter');
const unlockFilterMobile = document.getElementById('unlock-filter-mobile');
const dateFilterMobile = document.getElementById('date-filter-mobile');
const deviceModal = document.getElementById('device-modal');
const modalContent = document.getElementById('modal-content');
const closeModal = document.getElementById('close-modal');
const closeModalBtn = document.getElementById('close-modal-btn');
const modalTitle = document.getElementById('modal-title');
const modalModel = document.getElementById('modal-model');
const modalDh = document.getElementById('modal-dh');
const modalDate = document.getElementById('modal-date');
const modalUnlock = document.getElementById('modal-unlock');
const modalUnlockMethod = document.getElementById('modal-unlock-method');
const navbar = document.getElementById('navbar');
const themeToggle = document.getElementById('theme-toggle');
const resetFilters = document.getElementById('reset-filters');
const loadMore = document.getElementById('load-more');
const loadMoreBtn = document.getElementById('load-more-btn');
const viewGrid = document.getElementById('view-grid');
const viewList = document.getElementById('view-list');
const totalDevices = document.getElementById('total-devices');
const officialCount = document.getElementById('official-count');
const count2025 = document.getElementById('2025-count');
const dhCount = document.getElementById('dh-count');
const relatedDevices = document.getElementById('related-devices');

// 应用状态
let devices = [];
let filteredDevices = [];
let isDarkMode = false;
let currentLimit = 18;
let isLoadingMore = false;
let currentView = 'grid';
let copyToast = null; // 复制提示元素

// 从API获取数据
async function fetchDevices() {
try {
    loading.classList.remove('hidden');
    content.classList.add('hidden');
    error.classList.add('hidden');
    
    // 实际项目中使用真实API
    const response = await fetch('https://api.lacs.cc/MiDeviceCode.json');
    if (!response.ok) {
        throw new Error('网络响应错误');
    }
    
    const data = await response.json();
    // 使用实际数据中的unlock_type字段，不进行随机分配
    devices = data.list;
    
    // 更新统计数据
    updateStats();
    
    filteredDevices = [...devices].slice(0, currentLimit);
    renderDevices();
    
    loading.classList.add('hidden');
    content.classList.remove('hidden');
} catch (error) {
    console.error('获取设备数据失败:', error);
    loading.classList.add('hidden');
    error.classList.remove('hidden');
}
}

// 更新统计数据
function updateStats() {
totalDevices.textContent = devices.length;

// 统计官方可解锁设备数量
const officialDevices = devices.filter(device => device.unlock_type === 'official');
officialCount.textContent = officialDevices.length;

// 统计2025年发布的设备数量
const devices2025 = devices.filter(device => {
        const year = new Date(device.release_date).getFullYear();
        return year === 2025;
    });
    count2025.textContent = devices2025.length;
    
    // 统计不同设备代号数量
    const uniqueDh = [...new Set(devices.map(device => device.dh))];
    dhCount.textContent = uniqueDh.length;
}

// 渲染设备列表
function renderDevices() {
    devicesGrid.innerHTML = '';
    
    if (filteredDevices.length === 0) {
        emptyState.classList.remove('hidden');
        devicesGrid.classList.add('hidden');
        loadMore.classList.add('hidden');
    } else {
        emptyState.classList.add('hidden');
        devicesGrid.classList.remove('hidden');
        
        if (currentView === 'grid') {
            devicesGrid.className = 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6';
        } else {
            devicesGrid.className = 'grid grid-cols-1 gap-6';
        }
        
        filteredDevices.forEach((device, index) => {
            const deviceCard = document.createElement('div');
            deviceCard.className = `device-card animate-fade-in ${currentView === 'list' ? 'lg:col-span-4' : ''}`;
            deviceCard.style.animationDelay = `${index * 0.05}s`;
            
            const unlockType = UNLOCK_TYPE_UI[device.unlock_type] || { text: '未知', badgeClass: 'badge-gray', icon: 'fa-question' };
            
            deviceCard.innerHTML = `
                <div class="device-card-header">
                    <h3 class="font-bold text-gray-800">${device.model}</h3>
                    <span class="badge ${unlockType.badgeClass}">
                        <i class="fa ${unlockType.icon} mr-1"></i> ${unlockType.text}
                    </span>
                </div>
                <div class="device-card-body">
                    <div class="flex items-center mb-4">
                        <div class="bg-gray-100 p-2 rounded-lg mr-3">
                            <i class="fa fa-code text-primary"></i>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">设备代号</p>
                            <p class="font-medium text-gray-800">${device.dh}</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center mb-6">
                        <div class="bg-gray-100 p-2 rounded-lg mr-3">
                            <i class="fa fa-calendar text-purple-600"></i>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">发布日期</p>
                            <p class="font-medium text-gray-800">${formatDate(device.date)}</p>
                        </div>
                    </div>
                    
                    <button class="btn btn-primary w-full mt-2 view-details" data-id="${device.id}">
                        <i class="fa fa-info-circle mr-2"></i> 查看详情
                    </button>
                    <button class="btn btn-secondary w-full mt-2 copy-dh" data-dh="${device.dh}">
                        <i class="fa fa-clipboard mr-2"></i> 复制代号
                    </button>
                </div>
            `;
            
            devicesGrid.appendChild(deviceCard);
        });
        
        // 显示或隐藏加载更多按钮
        if (filteredDevices.length < devices.length) {
            loadMore.classList.remove('hidden');
        } else {
            loadMore.classList.add('hidden');
        }
    }
    
    // 更新设备计数
    deviceCount.textContent = `显示 ${filteredDevices.length}/${devices.length} 台设备`;
    
    // 添加查看详情按钮事件
    document.querySelectorAll('.view-details').forEach(button => {
        button.addEventListener('click', () => {
            const deviceId = button.getAttribute('data-id');
            openDeviceModal(deviceId);
        });
    });
    // 添加复制代号按钮事件
    document.querySelectorAll('.copy-dh').forEach(button => {
        button.addEventListener('click', () => {
            const deviceDh = button.getAttribute('data-dh');
            copyToClipboard(deviceDh);
        });
    });
}


// 复制文本到剪贴板
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showCopyToast(`已复制: ${text}`);
    }).catch(err => {
        console.error('复制失败:', err);
        showCopyToast('复制失败，请手动复制');
    });
}


// 显示复制提示
function showCopyToast(message) {
    // 如果已有提示，先移除
    if (copyToast) {
        document.body.removeChild(copyToast);
    }
    
    // 创建提示元素
    copyToast = document.createElement('div');
    copyToast.className = `fixed bottom-4 right-4 px-4 py-2 rounded-lg shadow-lg z-50 transition-opacity duration-300 ${
        isDarkMode ? 'bg-gray-800 text-white' : 'bg-green-500 text-white'
    }`;
    copyToast.textContent = message;
    
    // 添加到页面
    document.body.appendChild(copyToast);
    
    // 3秒后自动隐藏
    setTimeout(() => {
        copyToast.style.opacity = '0';
        setTimeout(() => {
            document.body.removeChild(copyToast);
            copyToast = null;
        }, 300);
    }, 3000);
}

// 打开设备详情模态框
function openDeviceModal(deviceId) {
    const device = devices.find(d => d.id === deviceId);
    if (!device) return;
    
    const unlockType = UNLOCK_TYPE_UI[device.unlock_type] || { text: '未知', badgeClass: 'badge-gray', icon: 'fa-question' };
    
    modalTitle.textContent = device.model;
    modalModel.textContent = device.model;
    modalDh.textContent = device.dh;
    modalDate.textContent = formatDate(device.release_date);
    
    modalUnlock.innerHTML = `
        <span class="badge ${unlockType.badgeClass}">
            <i class="fa ${unlockType.icon} mr-1"></i> ${unlockType.text}
        </span>
    `;
    
    // 渲染解锁方法步骤
    modalUnlockMethod.innerHTML = '';
    const steps = UNLOCK_METHODS[device.unlock_type] || ['暂未提供详细的解锁方法'];
    steps.forEach(step => {
        const stepElement = document.createElement('p');
        stepElement.className = 'flex items-start';
        stepElement.innerHTML = `
            <span class="text-primary font-bold mr-2">${step.split('.')[0]}</span>
            <span>${step.split('.')[1] || step}</span>
        `;
        modalUnlockMethod.appendChild(stepElement);
    });
    
    // 渲染相关设备
    renderRelatedDevices(device);
    
    // 显示模态框并添加动画
    deviceModal.classList.remove('hidden');
    setTimeout(() => {
        modalContent.classList.remove('scale-95', 'opacity-0');
        modalContent.classList.add('scale-100', 'opacity-100');
    }, 10);
    
    // 禁止背景滚动
    document.body.style.overflow = 'hidden';
}

// 渲染相关设备
function renderRelatedDevices(device) {
    relatedDevices.innerHTML = '';
    
    // 基于品牌和系列找到相关设备
    const related = devices
        .filter(d => 
            d.id !== device.id && 
            (d.brand === device.brand || d.series === device.series)
        )
        .slice(0, 4);
    
    if (related.length === 0) {
        relatedDevices.innerHTML = `
            <div class="col-span-full text-center py-6 text-gray-500">
                暂无相关设备
            </div>
        `;
        return;
    }
    
    related.forEach(relDevice => {
        const relUnlockType = UNLOCK_TYPE_UI[relDevice.unlock_type] || { text: '未知', badgeClass: 'badge-gray', icon: 'fa-question' };
        
        const relDeviceElement = document.createElement('div');
        relDeviceElement.className = 'bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors';
        relDeviceElement.innerHTML = `
            <div class="flex justify-between items-start mb-2">
                <h4 class="font-medium text-gray-800">${relDevice.model}</h4>
                <span class="badge ${relUnlockType.badgeClass} text-xs">
                    ${relUnlockType.text}
                </span>
            </div>
            <p class="text-sm text-gray-600 mb-2">${relDevice.dh}</p>
            <p class="text-xs text-gray-500">${formatDate(relDevice.release_date)}</p>
        `;
        
        relDeviceElement.addEventListener('click', () => {
            closeModalFunc();
            setTimeout(() => {
                openDeviceModal(relDevice.id);
            }, 300);
        });
        
        relatedDevices.appendChild(relDeviceElement);
    });
}

// 关闭模态框
function closeModalFunc() {
    modalContent.classList.remove('scale-100', 'opacity-100');
    modalContent.classList.add('scale-95', 'opacity-0');
    
    setTimeout(() => {
        deviceModal.classList.add('hidden');
        document.body.style.overflow = '';
    }, 300);
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '未知';
    
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;
    
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

// 筛选设备
function filterDevices() {
    const searchTerm = searchInput.value.toLowerCase().trim();
    const unlockFilterValue = unlockFilter.value;
    const dateFilterValue = dateFilter.value;
    
    let filtered = [...devices];
    
    // 按搜索词筛选
    if (searchTerm) {
        filtered = filtered.filter(device => 
            device.model.toLowerCase().includes(searchTerm) || 
            device.dh.toLowerCase().includes(searchTerm) || 
            formatDate(device.release_date).toLowerCase().includes(searchTerm)
        );
    }
    
    // 按解锁方式筛选
    if (unlockFilterValue !== 'all') {
        filtered = filtered.filter(device => device.unlock_type === unlockFilterValue);
    }
    
    // 按日期筛选
    if (dateFilterValue !== 'all') {
        const year = parseInt(dateFilterValue);
        filtered = filtered.filter(device => {
            const releaseYear = new Date(device.release_date).getFullYear();
            
            if (dateFilterValue === 'older') {
                return releaseYear < 2020;
            }
            
            return releaseYear === year;
        });
    }
    
    currentLimit = 18;
    filteredDevices = filtered.slice(0, currentLimit);
    renderDevices();
}

// 加载更多设备
function loadMoreDevices() {
    if (isLoadingMore) return;
    
    isLoadingMore = true;
    loadMoreBtn.innerHTML = '<i class="fa fa-spinner fa-spin mr-2"></i> 加载中...';
    
    setTimeout(() => {
        currentLimit += 12;
        
        const searchTerm = searchInput.value.toLowerCase().trim();
        const unlockFilterValue = unlockFilter.value;
        const dateFilterValue = dateFilter.value;
        
        let filtered = [...devices];
        
        // 按搜索词筛选
        if (searchTerm) {
            filtered = filtered.filter(device => 
                device.model.toLowerCase().includes(searchTerm) || 
                device.dh.toLowerCase().includes(searchTerm) || 
                formatDate(device.release_date).toLowerCase().includes(searchTerm)
            );
        }
        
        // 按解锁方式筛选
        if (unlockFilterValue !== 'all') {
            filtered = filtered.filter(device => device.unlock_type === unlockFilterValue);
        }
        
        // 按日期筛选
        if (dateFilterValue !== 'all') {
            const year = parseInt(dateFilterValue);
            filtered = filtered.filter(device => {
                const releaseYear = new Date(device.release_date).getFullYear();
                
                if (dateFilterValue === 'older') {
                    return releaseYear < 2020;
                }
                
                return releaseYear === year;
            });
        }
        
        filteredDevices = filtered.slice(0, currentLimit);
        renderDevices();
        
        isLoadingMore = false;
        loadMoreBtn.innerHTML = '<i class="fa fa-refresh mr-2"></i> 加载更多设备';
    }, 800);
}

// 切换视图
function switchView(view) {
    currentView = view;
    
    if (view === 'grid') {
        viewGrid.classList.add('bg-primary', 'text-white');
        viewGrid.classList.remove('border', 'border-gray-300', 'text-gray-700');
        
        viewList.classList.remove('bg-primary', 'text-white');
        viewList.classList.add('border', 'border-gray-300', 'text-gray-700');
    } else {
        viewList.classList.add('bg-primary', 'text-white');
        viewList.classList.remove('border', 'border-gray-300', 'text-gray-700');
        
        viewGrid.classList.remove('bg-primary', 'text-white');
        viewGrid.classList.add('border', 'border-gray-300', 'text-gray-700');
    }
    
    renderDevices();
}

// 重置筛选器
function resetAllFilters() {
    searchInput.value = '';
    clearSearch.classList.add('hidden');
    
    unlockFilter.value = 'all';
    dateFilter.value = 'all';
    unlockFilterMobile.value = 'all';
    dateFilterMobile.value = 'all';
    
    filterDevices();
}

// 初始化事件监听器
function initEventListeners() {
    // 重试按钮
    retryBtn.addEventListener('click', fetchDevices);
    
    // 刷新按钮
    refreshBtn.addEventListener('click', () => {
        refreshBtn.innerHTML = '<i class="fa fa-spinner fa-spin mr-2"></i> 刷新中...';
        fetchDevices();
        
        setTimeout(() => {
            refreshBtn.innerHTML = '<i class="fa fa-refresh mr-2"></i> 刷新数据';
        }, 1000);
    });
    
    // 搜索输入
    searchInput.addEventListener('input', () => {
        if (searchInput.value.trim()) {
            clearSearch.classList.remove('hidden');
        } else {
            clearSearch.classList.add('hidden');
        }
        
        filterDevices();
    });
    
    // 清除搜索
    clearSearch.addEventListener('click', () => {
        searchInput.value = '';
        clearSearch.classList.add('hidden');
        filterDevices();
    });
    
    // 解锁方式筛选
    unlockFilter.addEventListener('change', filterDevices);
    dateFilter.addEventListener('change', filterDevices);
    
    // 移动端筛选
    filterToggle.addEventListener('click', () => {
        mobileFilter.classList.remove('hidden');
        setTimeout(() => {
            mobileFilterPanel.classList.remove('-translate-x-full');
        }, 10);
    });
    
    closeFilter.addEventListener('click', () => {
        mobileFilterPanel.classList.add('-translate-x-full');
        setTimeout(() => {
            mobileFilter.classList.add('hidden');
        }, 300);
    });
    
    applyFilter.addEventListener('click', () => {
        unlockFilter.value = unlockFilterMobile.value;
        dateFilter.value = dateFilterMobile.value;
        
        mobileFilterPanel.classList.add('-translate-x-full');
        setTimeout(() => {
            mobileFilter.classList.add('hidden');
            filterDevices();
        }, 300);
    });
    
    // 模态框关闭
    closeModal.addEventListener('click', closeModalFunc);
    closeModalBtn.addEventListener('click', closeModalFunc);
    
    // 点击模态框外部关闭
    deviceModal.addEventListener('click', (e) => {
        if (e.target === deviceModal) {
            closeModalFunc();
        }
    });
    
    // 滚动事件 - 导航栏效果
    window.addEventListener('scroll', () => {
        if (window.scrollY > 50) {
            navbar.classList.add('shadow-md');
            navbar.classList.remove('shadow-sm');
        } else {
            navbar.classList.remove('shadow-md');
            navbar.classList.add('shadow-sm');
        }
    });
    
    // 主题切换
    themeToggle.addEventListener('click', () => {
        isDarkMode = !isDarkMode;
        
        if (isDarkMode) {
            document.body.classList.add('bg-gray-900', 'text-white');
            document.body.classList.remove('bg-neutral');
            
            themeToggle.innerHTML = '<i class="fa fa-sun-o"></i>';
            
            // 更新卡片样式
            document.querySelectorAll('.device-card').forEach(card => {
                card.classList.add('bg-gray-800');
                card.classList.remove('bg-white');
            });
            
            // 更新统计卡片
            document.querySelectorAll('.bg-white.rounded-xl').forEach(card => {
                card.classList.add('bg-gray-800');
                card.classList.remove('bg-white');
            });
            
            // 更新模态框
            document.querySelectorAll('#modal-content, #modal-unlock-method, .bg-gray-50').forEach(el => {
                el.classList.add('bg-gray-800');
                el.classList.remove('bg-white', 'bg-gray-50');
            });
            
            // 更新页脚
            document.querySelector('footer').classList.add('bg-gray-800', 'border-gray-700');
            document.querySelector('footer').classList.remove('bg-white', 'border-gray-200');
            
            // 更新导航栏
            document.querySelector('#navbar').classList.add('bg-gray-800/80');
            document.querySelector('#navbar').classList.remove('bg-white/80');
            
            // 更新筛选栏
            document.querySelector('#filter-bar').classList.add('bg-gray-800', 'border-gray-700');
            document.querySelector('#filter-bar').classList.remove('bg-white', 'border-gray-100');
            
            // 更新移动端筛选面板
            document.querySelector('#mobile-filter-panel').classList.add('bg-gray-800');
            document.querySelector('#mobile-filter-panel').classList.remove('bg-white');
            
            // 更新下拉菜单
            document.querySelectorAll('select').forEach(select => {
                select.classList.add('bg-gray-700', 'text-white', 'border-gray-600');
                select.classList.remove('bg-white', 'text-gray-800', 'border-gray-200');
            });
            
            // 更新输入框
            document.querySelector('#search-input').classList.add('bg-gray-700', 'text-white', 'border-gray-600');
            document.querySelector('#search-input').classList.remove('bg-white', 'text-gray-800', 'border-gray-200');
            
            // 更新按钮
            document.querySelectorAll('.btn-outline').forEach(btn => {
                btn.classList.add('border-gray-600', 'text-gray-300', 'hover:bg-gray-700');
                btn.classList.remove('border-gray-300', 'text-gray-700', 'hover:bg-gray-50');
            });
            
            // 更新相关设备卡片
            document.querySelectorAll('#related-devices > div').forEach(card => {
                card.classList.add('bg-gray-700', 'hover:bg-gray-600');
                card.classList.remove('bg-gray-50', 'hover:bg-gray-100');
            });
            // 更新复制提示样式
            if (copyToast) {
                copyToast.classList.remove('bg-green-500', 'text-white');
                copyToast.classList.add('bg-gray-800', 'text-white');
            }
        } else {
            document.body.classList.remove('bg-gray-900', 'text-white');
            document.body.classList.add('bg-neutral');
            
            themeToggle.innerHTML = '<i class="fa fa-moon-o"></i>';
            
            // 恢复卡片样式
            document.querySelectorAll('.device-card').forEach(card => {
                card.classList.remove('bg-gray-800');
                card.classList.add('bg-white');
            });
            
            // 恢复统计卡片
            document.querySelectorAll('.bg-gray-800.rounded-xl').forEach(card => {
                card.classList.remove('bg-gray-800');
                card.classList.add('bg-white');
            });
            
            // 恢复模态框
            document.querySelectorAll('#modal-content, #modal-unlock-method, .bg-gray-800').forEach(el => {
                el.classList.remove('bg-gray-800');
                el.classList.add('bg-white', 'bg-gray-50');
            });
            
            // 恢复页脚
            document.querySelector('footer').classList.remove('bg-gray-800', 'border-gray-700');
            document.querySelector('footer').classList.add('bg-white', 'border-gray-200');
            
            // 恢复导航栏
            document.querySelector('#navbar').classList.remove('bg-gray-800/80');
            document.querySelector('#navbar').classList.add('bg-white/80');
            
            // 恢复筛选栏
            document.querySelector('#filter-bar').classList.remove('bg-gray-800', 'border-gray-700');
            document.querySelector('#filter-bar').classList.add('bg-white', 'border-gray-100');
            
            // 恢复移动端筛选面板
            document.querySelector('#mobile-filter-panel').classList.remove('bg-gray-800');
            document.querySelector('#mobile-filter-panel').classList.add('bg-white');
            
            // 恢复下拉菜单
            document.querySelectorAll('select').forEach(select => {
                select.classList.remove('bg-gray-700', 'text-white', 'border-gray-600');
                select.classList.add('bg-white', 'text-gray-800', 'border-gray-200');
            });
            
            // 恢复输入框
            document.querySelector('#search-input').classList.remove('bg-gray-700', 'text-white', 'border-gray-600');
            document.querySelector('#search-input').classList.add('bg-white', 'text-gray-800', 'border-gray-200');
            
            // 恢复按钮
            document.querySelectorAll('.btn-outline').forEach(btn => {
                btn.classList.remove('border-gray-600', 'text-gray-300', 'hover:bg-gray-700');
                btn.classList.add('border-gray-300', 'text-gray-700', 'hover:bg-gray-50');
            });
            
            // 恢复相关设备卡片
            document.querySelectorAll('#related-devices > div').forEach(card => {
                card.classList.remove('bg-gray-700', 'hover:bg-gray-600');
                card.classList.add('bg-gray-50', 'hover:bg-gray-100');
            });
            // 更新复制提示样式
            if (copyToast) {
                copyToast.classList.remove('bg-gray-800', 'text-white');
                copyToast.classList.add('bg-green-500', 'text-white');
            }
        }
    });
    
    // 重置筛选器
    resetFilters.addEventListener('click', resetAllFilters);
    
    // 加载更多
    loadMoreBtn.addEventListener('click', loadMoreDevices);
    
    // 视图切换
    viewGrid.addEventListener('click', () => switchView('grid'));
    viewList.addEventListener('click', () => switchView('list'));
    
    // 初始视图
    switchView('grid');
}

// 初始化应用
function init() {
    initEventListeners();
    fetchDevices();
}

// 当DOM加载完成后初始化应用
document.addEventListener('DOMContentLoaded', init);