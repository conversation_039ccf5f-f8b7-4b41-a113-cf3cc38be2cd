<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>领创科技 - 分站展示</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #3b82f6;
            --secondary: #10b981;
            --accent: #8b5cf6;
            --dark: #1f2937;
            --gray-200: #e5e7eb;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .section {
            padding: 4rem 0;
        }

        .bg-gradient-to-br {
            background-image: linear-gradient(to bottom right, var(--from), var(--to));
        }

        .from-blue-500\/10 {
            --from: rgba(59, 130, 246, 0.1);
        }

        .to-blue-300\/10 {
            --to: rgba(96, 165, 250, 0.1);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1.5rem;
        }

        .section-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        h2 {
            font-size: clamp(1.5rem, 3vw, 2.5rem);
            font-weight: bold;
            color: var(--dark);
        }

        .text-gray-600 {
            color: var(--gray-600);
        }

        .max-w-2xl {
            max-width: 42rem;
        }

        .mx-auto {
            margin-left: auto;
            margin-right: auto;
        }

        .mt-4 {
            margin-top: 1rem;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(1, minmax(0, 1fr));
            gap: 1.5rem;
        }

        @media (min-width: 640px) {
            .grid {
                grid-template-columns: repeat(2, minmax(0, 1fr));
            }
        }

        @media (min-width: 1024px) {
            .grid {
                grid-template-columns: repeat(4, minmax(0, 1fr));
            }
        }

        .card-little {
            background-color: white;
            border-radius: 0.75rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card-little:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        .card-little-content {
            cursor: pointer;
        }

        .p-6 {
            padding: 1.5rem;
        }

        .text-center {
            text-align: center;
        }

        .w-16 {
            width: 4rem;
        }

        .h-16 {
            height: 4rem;
        }

        .rounded-full {
            border-radius: 9999px;
        }

        .flex {
            display: flex;
        }

        .items-center {
            align-items: center;
        }

        .justify-center {
            justify-content: center;
        }

        .mx-auto {
            margin-left: auto;
            margin-right: auto;
        }

        .mb-4 {
            margin-bottom: 1rem;
        }

        .bg-primary\/10 {
            background-color: rgba(59, 130, 246, 0.1);
        }

        .bg-secondary\/10 {
            background-color: rgba(16, 185, 129, 0.1);
        }

        .bg-accent\/10 {
            background-color: rgba(139, 92, 246, 0.1);
        }

        .bg-dark\/10 {
            background-color: rgba(31, 41, 55, 0.1);
        }

        .w-8 {
            width: 2rem;
        }

        .h-8 {
            height: 2rem;
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .text-xl {
            font-size: 1.25rem;
        }

        .font-bold {
            font-weight: bold;
        }

        .mb-2 {
            margin-bottom: 0.5rem;
        }

        .fa-2x {
            font-size: 2em;
        }

        .text-primary {
            color: var(--primary);
        }

        .text-secondary {
            color: var(--secondary);
        }

        .text-accent {
            color: var(--accent);
        }

        .text-dark {
            color: var(--dark);
        }

        .loading {
            text-align: center;
            padding: 2rem 0;
            color: var(--gray-600);
        }
    </style>
</head>
<body>
<!-- 项目列表部分 -->
<div class="section min-h-screen py-16" id="projects-section">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 w-full">
        <div class="section-header text-center mb-12">
            <h2 class="text-[clamp(1.5rem,3vw,2.5rem)] font-bold text-dark">项目列表</h2>
            <p class="text-gray-600 mt-4 max-w-2xl mx-auto"></p>
        </div>
        <div class="btn-group flex flex-wrap justify-center gap-3 mb-10 w-full" id="project-buttons">
            <!-- 按钮将通过数据动态生成 -->
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 w-full" id="project-cards-container">
            <div class="loading">
                <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
                <p>加载项目数据中...</p>
            </div>
        </div>
    </div>
</div>

<!-- 分站部分 -->
<div class="section py-16 bg-gradient-to-br from-blue-500/10 to-blue-300/10" id="sites-section">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="section-header text-center mb-12">
            <h2 class="text-[clamp(1.5rem,3vw,2.5rem)] font-bold text-dark">其他分站</h2>
            <p class="text-gray-600 mt-4 max-w-2xl mx-auto">探索我们的更多服务与资源，满足您的多样化需求</p>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6" id="sites-container">
            <div class="loading">
                <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
                <p>加载分站数据中...</p>
            </div>
        </div>
    </div>
</div>

<script>
    // 获取DOM元素
    const projectButtonsContainer = document.getElementById('project-buttons');
    const projectCardsContainer = document.getElementById('project-cards-container');
    const sitesContainer = document.getElementById('sites-container');
    let projectsData = [];
    let sitesData = [];

    // 打开链接函数
    function openLink(url) {
        window.open(url, '_blank');
    }

    // 加载JSON数据
    async function loadJsonData(url) {
        try {
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error('加载数据失败:', error);
            throw error;
        }
    }

    // 加载项目数据
    async function loadProjectsData() {
        try {
            projectsData = await loadJsonData('projects.json');
            return projectsData;
        } catch (error) {
            projectCardsContainer.innerHTML = `
                    <div class="loading">
                        <i class="fas fa-exclamation-circle fa-2x mb-2 text-red-500"></i>
                        <p>加载项目数据失败，请检查网络连接</p>
                    </div>
                `;
            throw error;
        }
    }

    // 加载分站数据
    async function loadSitesData() {
        try {
            sitesData = await loadJsonData('sites.json');
            return sitesData;
        } catch (error) {
            sitesContainer.innerHTML = `
                    <div class="loading">
                        <i class="fas fa-exclamation-circle fa-2x mb-2 text-red-500"></i>
                        <p>加载分站数据失败，请检查网络连接</p>
                    </div>
                `;
            throw error;
        }
    }

    // 生成项目分类按钮
    function generateProjectCategoryButtons() {
        projectButtonsContainer.innerHTML = ''; // 清空容器

        // 获取唯一的分类
        const categories = [...new Set(projectsData.map(project => project.category))];

        // 创建"全部项目"按钮
        const allButton = document.createElement('button');
        allButton.type = 'button';
        allButton.className = 'btn-filter active bg-primary text-white px-5 py-2.5 rounded-lg';
        allButton.dataset.project = 'all';
        allButton.textContent = '全部项目';
        allButton.addEventListener('click', () => filterProjects('all'));
        projectButtonsContainer.appendChild(allButton);

        // 创建分类按钮
        categories.forEach(category => {
            const categoryName = projectsData.find(p => p.category === category)?.categoryName;
            if (categoryName) {
                const button = document.createElement('button');
                button.type = 'button';
                button.className = 'btn-filter bg-gray-200 hover:bg-primary hover:text-white px-5 py-2.5 rounded-lg';
                button.dataset.project = category;
                button.textContent = categoryName;
                button.addEventListener('click', () => filterProjects(category));
                projectButtonsContainer.appendChild(button);
            }
        });
    }

    // 生成项目卡片
    function generateProjectCards(projects) {
        projectCardsContainer.innerHTML = ''; // 清空容器

        if (projects.length === 0) {
            projectCardsContainer.innerHTML = `
                    <div class="loading">
                        <i class="fas fa-search fa-2x mb-2 text-gray-400"></i>
                        <p>没有找到匹配的项目</p>
                    </div>
                `;
            return;
        }

        projects.forEach(project => {
            const card = document.createElement('div');
            card.className = 'project-card bg-white rounded-xl shadow-md overflow-hidden card-hover w-full';
            card.dataset.category = project.category;

            card.innerHTML = `
                    <div class="p-6">
                        <div class="card-icon-wrapper mb-4 w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                            <i class="fa ${project.icon} fa-xl text-primary"></i>
                        </div>
                        <h5 class="card-title text-xl font-bold mb-2">${project.title}</h5>
                        <p class="card-text text-gray-600 mb-4">${project.description}</p>
                        <div class="flex items-center text-sm text-gray-500 mb-4">
                            <span class="flex items-center mr-3"><i class="fa fa-code mr-1"></i> ${project.platform}</span>
                            ${project.updateDate ?
                `<span class="flex items-center"><i class="fa fa-calendar mr-1"></i> 更新于 ${project.updateDate}</span>` :
                ''}
                        </div>
                    </div>
                    <div class="px-6 pb-6">
                        <a href="${project.link}" class="btn-primary block text-center" target="_blank" rel="noopener noreferrer" data-umami-event="访问项目${project.category}-${project.id}">
                            立即访问
                        </a>
                    </div>
                `;

            projectCardsContainer.appendChild(card);
        });
    }

    // 筛选项目
    function filterProjects(category) {
        // 更新按钮状态
        document.querySelectorAll('.btn-filter').forEach(button => {
            button.classList.remove('active', 'bg-primary', 'text-white');
            button.classList.add('bg-gray-200');
        });

        const activeButton = document.querySelector(`[data-project="${category}"]`);
        if (activeButton) {
            activeButton.classList.remove('bg-gray-200');
            activeButton.classList.add('active', 'bg-primary', 'text-white');
        }

        // 筛选项目
        let filteredProjects;
        if (category === 'all') {
            filteredProjects = projectsData;
        } else {
            filteredProjects = projectsData.filter(project => project.category === category);
        }

        // 生成筛选后的项目卡片
        generateProjectCards(filteredProjects);
    }

    // 生成分站卡片
    function generateSitesCards() {
        sitesContainer.innerHTML = ''; // 清空容器

        if (sitesData.length === 0) {
            sitesContainer.innerHTML = `
                    <div class="loading">
                        <i class="fas fa-search fa-2x mb-2 text-gray-400"></i>
                        <p>没有找到分站数据</p>
                    </div>
                `;
            return;
        }

        sitesData.forEach(site => {
            const card = document.createElement('div');
            card.className = 'card-little bg-white rounded-xl shadow-md overflow-hidden card-hover';

            // 根据图标类型生成不同的图标内容
            let iconContent = '';
            if (site.iconType === 'image') {
                iconContent = `<img src="${site.iconContent}" class="w-8 h-8" alt="${site.name}">`;
            } else if (site.iconType === 'font-awesome') {
                iconContent = `<i class="fa ${site.iconContent} fa-2x text-${site.bgColor}"></i>`;
            }

            card.innerHTML = `
                    <div class="card-little-content cursor-pointer" onclick="openLink('${site.link}')" data-umami-event="${site.analyticsEvent}">
                        <div class="p-6 text-center">
                            <div class="w-16 h-16 bg-${site.bgColor}/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                ${iconContent}
                            </div>
                            <h5 class="card-title text-xl font-bold mb-2">${site.name}</h5>
                            <p class="text-gray-600">${site.description}</p>
                        </div>
                    </div>
                `;

            sitesContainer.appendChild(card);
        });
    }

    // 初始化页面
    async function initPage() {
        try {
            // 并行加载项目和分站数据
            await Promise.all([loadProjectsData(), loadSitesData()]);

            // 生成项目和分站内容
            generateProjectCategoryButtons();
            generateProjectCards(projectsData);
            generateSitesCards();
        } catch (error) {
            console.error('初始化页面失败:', error);
        }
    }

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', initPage);
</script>
</body>
</html>