<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="领创工作室专注于数码科技领域，提供原创软件工具、技术教程和创意内容，涵盖编程、设计、摄影、音乐等多个方面。">
    <meta name="keywords" content="领创工作室,领创,LACS,领创科技,数码科技创新,原创软件工具,编程教程,设计资源,摄影作品,音乐创作">
    <meta property="og:title" content="领创工作室 - 数码科技与创意内容平台" />
    <meta property="og:description" content="探索领创工作室，获取专业的数码科技解决方案，包括原创软件工具、技术教程和创意内容资源。" />
    <meta property="og:image" content="https://gitee.com/lacsgf/img/raw/master/webp/bg-lacs-group.webp" />
    <meta name="robots" content="index, follow" />
    <!-- 保留原有canonical标签和 -->
    <link rel="canonical" href="https://lacs.cc/" />
    <!-- 引入Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入Font Awesome -->
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css" rel="stylesheet">

    <!-- 引入粒子效果库 -->
    <script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
    <link rel="icon" href="img/favicon.ico" type="image/x-icon">
    <title>领创工作室-官方网站-lacs.cc</title>
    <script src="css/style.css"></script>
    <!-- Tailwind配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#EBF5FF',
                            100: '#E1EFFE',
                            200: '#C3DDFD',
                            300: '#A4CAFE',
                            400: '#76A9FA',
                            500: '#3F83F8',
                            600: '#1C64F2',
                            700: '#1A56DB',
                            800: '#1E429F',
                            900: '#233876',
                            DEFAULT: '#1C64F2'
                        },
                        secondary: {
                            50: '#EDFAFA',
                            100: '#D5F5F6',
                            200: '#AFECEF',
                            300: '#7EDCE2',
                            400: '#16BDCA',
                            500: '#0694A2',
                            600: '#047481',
                            700: '#036672',
                            800: '#05505C',
                            900: '#014451',
                            DEFAULT: '#0694A2'
                        },
                        accent: {
                            50: '#FFF9EC',
                            100: '#FFF3C1',
                            200: '#FFECA1',
                            300: '#FFDC48',
                            400: '#F3C000',
                            500: '#D4A200',
                            600: '#A67F00',
                            700: '#805C00',
                            800: '#5A4000',
                            900: '#3A2800',
                            DEFAULT: '#F3C000'
                        },
                        dark: '#0F172A',
                        light: '#F8FAFC'
                    },
                    fontFamily: {
                        inter: ['Inter', 'system-ui', 'sans-serif'],
                        mono: ['JetBrains Mono', 'monospace']
                    },
                    animation: {
                        'pulse-slow': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'float': 'float 6s ease-in-out infinite',
                        'spin-slow': 'spin 8s linear infinite',
                        'glow': 'glow 2s ease-in-out infinite alternate',
                        'gradient': 'gradient 8s ease infinite',
                        'typing': 'typing 3.5s steps(40, end), blink-caret .75s step-end infinite'
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0)' },
                            '50%': { transform: 'translateY(-20px)' }
                        },
                        glow: {
                            '0%': { boxShadow: '0 0 5px rgba(28, 100, 242, 0.5)' },
                            '100%': { boxShadow: '0 0 20px rgba(28, 100, 242, 0.8)' }
                        },
                        gradient: {
                            '0%': { backgroundPosition: '0% 50%' },
                            '50%': { backgroundPosition: '100% 50%' },
                            '100%': { backgroundPosition: '0% 50%' }
                        },
                        typing: {
                            'from': { width: '0' },
                            'to': { width: '100%' }
                        },
                        'blink-caret': {
                            'from, to': { borderColor: 'transparent' },
                            '50%': { borderColor: 'currentColor' }
                        }
                    },
                    boxShadow: {
                        'neon': '0 0 5px theme(\'colors.primary.400\'), 0 0 20px theme(\'colors.primary.600\')',
                        'neon-secondary': '0 0 5px theme(\'colors.secondary.400\'), 0 0 20px theme(\'colors.secondary.600\')',
                        'neon-accent': '0 0 5px theme(\'colors.accent.400\'), 0 0 20px theme(\'colors.accent.600\')',
                        'glass': '0 8px 32px 0 rgba(31, 38, 135, 0.37)'
                    },
                    backdropBlur: {
                        'xs': '2px'
                    }
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            /* 容器间距 */
            .container {
                @apply px-4 sm:px-6 lg:px-8;
            }
            
            /* 统一卡片样式 */
            .unified-card {
                @apply bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden shadow-md transition-all duration-300;
                @apply hover:shadow-lg hover:-translate-y-1;
                @apply w-full h-full flex flex-col;
            }
            
            /* 卡片头部 */
            .card-header {
                @apply p-5 border-b border-gray-200 dark:border-gray-700 bg-gray-50/80 dark:bg-gray-900/50;
            }
            
            /* 卡片内容 */
            .card-body {
                @apply p-6 flex-grow;
            }
            
            /* 卡片底部 */
            .card-footer {
                @apply p-5 border-t border-gray-200 dark:border-gray-700 bg-gray-50/80 dark:bg-gray-900/50;
            }
            
            /* 标题样式 */
            h1, h2, h3, h4, h5, h6 {
                @apply text-gray-900 dark:text-white font-bold;
            }
            
            h1 { @apply text-4xl }
            h2 { @apply text-3xl }
            h3 { @apply text-2xl }
            h4 { @apply text-xl }
            h5 { @apply text-lg }
            h6 { @apply text-base }
            
            /* 正文样式 */
            p {
                @apply text-gray-700 dark:text-gray-300 leading-relaxed;
            }
            
            /* 按钮样式 */
            .btn-primary {
                @apply bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-primary-600 hover:shadow-neon transition-all duration-300;
                @apply border-2 border-primary/20 flex items-center justify-center gap-2;
            }
            
            .btn-secondary {
                @apply bg-white dark:bg-gray-800 text-primary dark:text-primary-400 border-2 border-primary/30 px-6 py-3 rounded-lg font-medium;
                @apply hover:bg-primary/5 dark:hover:bg-primary-900/20 hover:border-primary transition-all duration-300;
                @apply flex items-center justify-center gap-2;
            }
            
            /* 表单控件 */
            input[type="text"], input[type="url"], input[type="email"], textarea {
                @apply w-full px-4 py-2 bg-white/80 dark:bg-gray-700/50 backdrop-blur-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-transparent transition-all duration-300;
            }
            
            /* 图片样式 */
            img {
                @apply max-w-full h-auto rounded-lg;
            }
            
            /* 章节标题样式 */
            .section-header {
                @apply mb-10 pb-6 border-b border-gray-200 dark:border-gray-700;
            }
            
            .section-header h2 {
                @apply text-3xl font-bold text-primary dark:text-primary-400;
            }
        }
    </style>

</head>
<body class="font-inter bg-gray-50 text-gray-800">
    <!-- 导航栏 -->
    <nav class="fixed top-0 left-0 right-0 z-50 transition-all duration-500 nav-default bg-white/80 backdrop-blur-sm p-4" id="main-nav">
        <div class="container mx-auto flex justify-between items-center">
            <a class="flex items-center group" href="#">
                <div class="relative overflow-hidden rounded-full p-0.5 mr-2 bg-gradient-to-r from-primary-400 to-secondary-400 animate-pulse-slow">
                    <img src="img/lacs.webp" class="h-8 w-8 rounded-full object-cover transform group-hover:scale-110 transition-transform duration-300" alt="领创工作室Logo">
                </div>
                <span class="text-xl font-bold gradient-text">领创工作室</span>
            </a>
            <button class="navbar-toggler md:hidden text-gray-700 focus:outline-none hover:text-gray-900 transition-colors duration-300" type="button" id="menu-toggle" aria-label="切换菜单">
                <i class="fas fa-bars text-xl"></i>
            </button>
            <div class="hidden md:flex items-center space-x-8" id="navbar-menu">
                <a href="#" class="relative text-gray-700 hover:text-gray-900 transition-colors duration-300 group">
                    <span>首页</span>
                    <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gray-900 group-hover:w-full transition-all duration-300"></span>
                </a>
                <a href="#projects-section" class="relative text-gray-700 hover:text-gray-900 transition-colors duration-300 group">
                    <span>项目</span>
                    <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gray-900 group-hover:w-full transition-all duration-300"></span>
                </a>
                <a href="#sites-section" class="relative text-gray-700 hover:text-gray-900 transition-colors duration-300 group">
                    <span>分站</span>
                    <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gray-900 group-hover:w-full transition-all duration-300"></span>
                </a>
                <a href="#about" class="relative text-gray-700 hover:text-gray-900 transition-colors duration-300 group">
                    <span>关于我们</span>
                    <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-gray-900 group-hover:w-full transition-all duration-300"></span>
                </a>
                <button class="btn-dark-mode text-gray-700 hover:text-gray-900 transition-all duration-300 transform hover:rotate-12" id="dark-mode-toggle" aria-label="切换深色模式">
                    <i class="fas fa-moon text-xl"></i>
                </button>
            </div>
        </div>
        <!-- 移动端菜单 -->
        <div class="md:hidden bg-white/80 backdrop-blur-sm hidden transition-all duration-500 border-t border-gray-200 p-4" id="mobile-menu">
            <div class="container mx-auto flex flex-col space-y-4">
                <a href="#" class="text-gray-700 hover:text-gray-900 hover:pl-2 transition-all duration-300 py-2 border-b border-gray-200">
                    <i class="fas fa-home mr-2 text-primary-400"></i> 首页
                </a>
                <a href="#projects-section" class="text-gray-700 hover:text-gray-900 hover:pl-2 transition-all duration-300 py-2 border-b border-gray-200">
                    <i class="fas fa-project-diagram mr-2 text-primary-400"></i> 项目
                </a>
                <a href="#sites-section" class="text-gray-700 hover:text-gray-900 hover:pl-2 transition-all duration-300 py-2 border-b border-gray-200">
                    <i class="fas fa-sitemap mr-2 text-primary-400"></i> 分站
                </a>
                <a href="#about" class="text-gray-700 hover:text-gray-900 hover:pl-2 transition-all duration-300 py-2 border-b border-gray-200">
                    <i class="fas fa-info-circle mr-2 text-primary-400"></i> 关于我们
                </a>
                <button class="flex items-center text-gray-700 hover:text-gray-900 hover:pl-2 transition-all duration-300 py-2" id="mobile-dark-mode-toggle" aria-label="切换深色模式">
                    <i class="fas fa-moon mr-2 text-primary-400"></i> 深色模式
                </button>
            </div>
        </div>
    </nav>

    <!-- 英雄区域 -->
    <section class="pt-28 pb-16 min-h-screen flex items-center relative overflow-hidden">
        <!-- 粒子背景 -->
        <div id="particles-js" class="absolute inset-0 z-0"></div>
        
        <!-- 背景动效元素 -->
        <div class="absolute inset-0 overflow-hidden pointer-events-none">
            <div class="absolute -top-10 -left-10 w-64 h-64 bg-primary-400/10 rounded-full blur-3xl animate-pulse"></div>
            <div class="absolute top-1/4 right-0 w-96 h-96 bg-secondary-400/10 rounded-full blur-3xl animate-pulse" style="animation-delay: 1s;"></div>
            <div class="absolute bottom-20 left-1/3 w-80 h-80 bg-accent-400/10 rounded-full blur-3xl animate-pulse" style="animation-delay: 2s;"></div>
            
            <!-- 科技感网格背景 -->
            <div class="absolute inset-0 bg-grid-pattern opacity-5"></div>
            
            <!-- 装饰线条 -->
            <div class="absolute top-1/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary-400/30 to-transparent"></div>
            <div class="absolute top-3/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-secondary-400/30 to-transparent"></div>
        </div>

        <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="flex flex-col md:flex-row items-center justify-center">
                <!-- 科技感装饰元素 -->
                <div class="md:w-1/2 flex justify-center mt-10 md:mt-0 relative perspective-1000">
                    <img src="img/bg-lacs-group.webp"
                         alt="领创工作室团队展示"
                         class="max-w-full h-auto rounded-xl shadow-neon-primary animate-float object-cover transform transition-all duration-700 hover:scale-105 hover:shadow-neon-primary"
                         style="max-height: 600px;">
                    <!-- 添加科技感装饰元素 -->
                    <div class="absolute -bottom-6 -left-6 w-24 h-24 bg-primary/10 rounded-full blur-2xl"></div>
                    <div class="absolute -top-6 -right-6 w-32 h-32 bg-secondary/10 rounded-full blur-2xl"></div>
                </div>

                <div class="md:w-1/2 mb-8 md:mb-0 transform transition-all duration-700 hover:translate-y-[-10px] md:pl-6 lg:pl-10 xl:pl-14 md:pr-2 lg:pr-4 xl:pr-6">

                    <h1 class="text-[clamp(2rem,5vw,3.5rem)] font-bold text-dark leading-tight mb-2">
                        <span class="gradient-text">领创工作室</span><br>
                        <span class="typing-effect text-primary-600 inline-block">Lead And Create Studio</span>
                    </h1>
                    <p class="mt-4 text-lg text-gray-600 max-w-xl opacity-90 hover:opacity-100 transition-opacity duration-300 border-l-4 border-primary-400/30 pl-4">
                        我们致力于开发高质量的原创软件和工具，分享数码科技知识，为您带来更好的数字体验。
                    </p>
                    <div class="mt-8 flex flex-wrap gap-4">
                        <a href="#projects-section"
                           class="btn-primary px-6 py-3 rounded-lg flex items-center gap-2 shadow-neon transform hover:-translate-y-1 transition-all duration-300">
                            <i class="fas fa-rocket"></i> 探索项目
                        </a>
                        <a href="#about"
                           class="btn-secondary px-6 py-3 rounded-lg flex items-center gap-2 transform hover:-translate-y-1 transition-all duration-300">
                            <i class="fas fa-info-circle"></i> 关于我们
                        </a>
                    </div>
                    
                    <!-- 技术标签 -->
                    <div class="mt-8 flex flex-wrap gap-2">
                        <span class="bg-primary-50 text-primary-700 text-xs px-3 py-1 rounded-full border border-primary-100">软件开发</span>
                        <span class="bg-secondary-50 text-secondary-700 text-xs px-3 py-1 rounded-full border border-secondary-100">数码科技</span>
                        <span class="bg-accent-50 text-accent-700 text-xs px-3 py-1 rounded-full border border-accent-100">创意设计</span>
                        <span class="bg-gray-50 text-gray-700 text-xs px-3 py-1 rounded-full border border-gray-100">技术分享</span>
                    </div>
                </div>

            </div>
        </div>
        
        <!-- 向下滚动指示器 -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
            <a href="#projects-section" class="text-primary-600 hover:text-primary-800 transition-colors duration-300">
                <i class="fas fa-chevron-down text-2xl"></i>
            </a>
        </div>
    </section>


    <!-- 初始化粒子效果 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof particlesJS !== 'undefined') {
                particlesJS("particles-js", {
                    "particles": {
                        "number": {
                            "value": 80,
                            "density": {
                                "enable": true,
                                "value_area": 800
                            }
                        },
                        "color": {
                            "value": "#1C64F2"
                        },
                        "shape": {
                            "type": "circle",
                            "stroke": {
                                "width": 0,
                                "color": "#000000"
                            },
                            "polygon": {
                                "nb_sides": 5
                            }
                        },
                        "opacity": {
                            "value": 0.3,
                            "random": false,
                            "anim": {
                                "enable": false,
                                "speed": 1,
                                "opacity_min": 0.1,
                                "sync": false
                            }
                        },
                        "size": {
                            "value": 3,
                            "random": true,
                            "anim": {
                                "enable": false,
                                "speed": 40,
                                "size_min": 0.1,
                                "sync": false
                            }
                        },
                        "line_linked": {
                            "enable": true,
                            "distance": 150,
                            "color": "#1C64F2",
                            "opacity": 0.2,
                            "width": 1
                        },
                        "move": {
                            "enable": true,
                            "speed": 2,
                            "direction": "none",
                            "random": false,
                            "straight": false,
                            "out_mode": "out",
                            "bounce": false,
                            "attract": {
                                "enable": false,
                                "rotateX": 600,
                                "rotateY": 1200
                            }
                        }
                    },
                    "interactivity": {
                        "detect_on": "canvas",
                        "events": {
                            "onhover": {
                                "enable": true,
                                "mode": "grab"
                            },
                            "onclick": {
                                "enable": true,
                                "mode": "push"
                            },
                            "resize": true
                        },
                        "modes": {
                            "grab": {
                                "distance": 140,
                                "line_linked": {
                                    "opacity": 1
                                }
                            },
                            "bubble": {
                                "distance": 400,
                                "size": 40,
                                "duration": 2,
                                "opacity": 8,
                                "speed": 3
                            },
                            "repulse": {
                                "distance": 200,
                                "duration": 0.4
                            },
                            "push": {
                                "particles_nb": 4
                            },
                            "remove": {
                                "particles_nb": 2
                            }
                        }
                    },
                    "retina_detect": true
                });
            }
        });
    </script>


    <!-- 项目区域 -->
    <div class="section py-20 relative overflow-hidden" id="projects-section">
        <!-- 背景装饰 -->
        <div class="absolute inset-0 bg-gradient-to-br from-gray-50 to-white z-0"></div>
        <div class="absolute inset-0 bg-grid-pattern opacity-5 z-0"></div>
        <div class="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary-400/30 to-transparent"></div>
        <div class="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary-400/30 to-transparent"></div>
        
        <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="section-header text-center mb-16">
                <div class="tech-border p-0.5 inline-block mb-2 mx-auto">
                    <span class="bg-white/80 backdrop-blur-sm px-3 py-1 text-xs font-semibold text-primary-600 rounded-full inline-block">创新项目</span>
                </div>
                <h2 class="text-[clamp(1.5rem,3vw,2.5rem)] font-bold gradient-text inline-block">项目列表</h2>
                <p class="text-gray-600 mt-4 max-w-2xl mx-auto">这里展示了我们开发的各类项目，通过分类筛选可以快速找到你感兴趣的内容</p>
            </div>
            
            <!-- 项目分类按钮 -->
            <div class="btn-group flex flex-wrap justify-center gap-3 mb-12 w-full" id="project-buttons">
                <!-- 按钮将通过数据动态生成，添加默认样式 -->
                <button class="tech-button px-4 py-2 rounded-full bg-white border border-gray-200 text-gray-700 hover:border-primary-400 hover:text-primary-600 transition-all duration-300 shadow-sm hover:shadow flex items-center gap-2">
                    <i class="fas fa-layer-group"></i> <span>全部项目</span>
                </button>
            </div>
            
            <!-- 项目卡片容器 -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 w-full" id="project-cards-container">
                <!-- 加载动画 -->
                <div class="loading col-span-full flex flex-col items-center justify-center py-16">
                    <div class="tech-icon-container mb-6">
                        <i class="fas fa-circle-notch fa-spin fa-3x text-primary-500"></i>
                    </div>
                    <p class="text-gray-600 font-medium typing-effect">正在加载项目数据...</p>
                </div>
                
                <!-- 项目卡片占位 -->
                <div class="card-3d bg-white rounded-xl shadow-lg p-6 h-72 relative group transform transition-all duration-500 hover:-translate-y-2 hover:shadow-xl border border-gray-100 overflow-hidden hidden">
                    <div class="absolute top-0 right-0 w-20 h-20 bg-gradient-to-bl from-primary-400/10 to-transparent rounded-bl-3xl"></div>
                    <div class="animate-pulse">
                        <div class="tech-icon-container mb-4">
                            <div class="w-10 h-10 bg-primary-100 rounded-lg"></div>
                        </div>
                        <div class="h-6 bg-gray-200 rounded-full w-3/4 mb-3"></div>
                        <div class="h-4 bg-gray-100 rounded-full w-1/2 mb-6"></div>
                        <div class="h-24 bg-gray-100 rounded-lg mb-4"></div>
                        <div class="flex justify-between items-center">
                            <div class="h-4 bg-gray-200 rounded-full w-1/4"></div>
                            <div class="h-8 w-8 bg-gray-200 rounded-full"></div>
                        </div>
                    </div>
                    <div class="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-primary-400 to-secondary-400 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 分站区域 -->
    <div class="section py-20 relative overflow-hidden" id="sites-section">
        <!-- 背景装饰 -->
        <div class="absolute inset-0 bg-gradient-to-br from-primary-50/30 to-secondary-50/30 z-0"></div>
        <div class="absolute inset-0 bg-grid-pattern opacity-5 z-0"></div>
        
        <!-- 装饰元素 -->
        <div class="absolute top-0 right-0 w-96 h-96 bg-primary-400/5 rounded-full blur-3xl"></div>
        <div class="absolute bottom-0 left-0 w-80 h-80 bg-secondary-400/5 rounded-full blur-3xl"></div>
        
        <!-- 装饰线条 -->
        <div class="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-secondary-400/30 to-transparent"></div>
        <div class="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary-400/30 to-transparent"></div>
        
        <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="section-header text-center mb-16">
                <div class="tech-border p-0.5 inline-block mb-2 mx-auto">
                    <span class="bg-white/80 backdrop-blur-sm px-3 py-1 text-xs font-semibold text-secondary-600 rounded-full inline-block">资源导航</span>
                </div>
                <h2 class="text-[clamp(1.5rem,3vw,2.5rem)] font-bold gradient-text inline-block">其他分站</h2>
                <p class="text-gray-600 mt-4 max-w-2xl mx-auto">探索我们的更多服务与资源，满足您的多样化需求</p>
            </div>
            
            <!-- 分站卡片容器 -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8" id="sites-container">
                <!-- 加载动画 -->
                <div class="loading col-span-full flex flex-col items-center justify-center py-16">
                    <div class="tech-icon-container mb-6">
                        <i class="fas fa-circle-notch fa-spin fa-3x text-secondary-500"></i>
                    </div>
                    <p class="text-gray-600 font-medium typing-effect">正在加载分站数据...</p>
                </div>
                
                <!-- 分站卡片占位 -->
                <div class="glass-card rounded-xl p-6 relative group transform transition-all duration-500 hover:-translate-y-2 hover:shadow-xl overflow-hidden hidden">
                    <div class="absolute top-0 right-0 w-full h-full bg-gradient-to-br from-primary-400/10 via-transparent to-secondary-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <div class="relative z-10">
                        <div class="tech-icon-container mb-4 bg-white/80 backdrop-blur-sm p-3 rounded-lg inline-block">
                            <div class="w-10 h-10 bg-primary-100 rounded-lg"></div>
                        </div>
                        <h3 class="text-xl font-bold mb-2">分站名称</h3>
                        <p class="text-gray-600 mb-4 text-sm">分站描述内容</p>
                        <a href="#" class="inline-flex items-center text-primary-600 hover:text-primary-800 transition-colors">
                            <span>访问</span>
                            <i class="fas fa-arrow-right ml-2 transform group-hover:translate-x-1 transition-transform duration-300"></i>
                        </a>
                    </div>
                    <div class="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-primary-400 to-secondary-400 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"></div>
                </div>
            </div>
        </div>
    </div>


    <!-- 关于 -->
    <div class="section py-20 relative overflow-hidden" id="about">
        <!-- 背景装饰 -->
        <div class="absolute inset-0 bg-gradient-to-br from-white to-gray-50 z-0"></div>
        <div class="absolute inset-0 bg-grid-pattern opacity-5 z-0"></div>
        
        <!-- 装饰元素 -->
        <div class="absolute top-20 left-0 w-72 h-72 bg-accent-400/5 rounded-full blur-3xl"></div>
        <div class="absolute bottom-20 right-0 w-96 h-96 bg-primary-400/5 rounded-full blur-3xl"></div>
        
        <!-- 装饰线条 -->
        <div class="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-accent-400/30 to-transparent"></div>
        <div class="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary-400/30 to-transparent"></div>
        
        <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="section-header text-center mb-16">
                <div class="tech-border p-0.5 inline-block mb-2 mx-auto">
                    <span class="bg-white/80 backdrop-blur-sm px-3 py-1 text-xs font-semibold text-accent-600 rounded-full inline-block">团队介绍</span>
                </div>
                <h2 class="text-[clamp(1.5rem,3vw,2.5rem)] font-bold gradient-text inline-block">关于我们</h2>
                <p class="text-gray-600 mt-4 max-w-2xl mx-auto">了解领创工作室的故事与使命，与我们一起探索数字世界的无限可能</p>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!--媒体平台-->
                <div id="platforms-glass-container" class="glass-card rounded-xl p-6 card-3d transform transition-all duration-500 hover:-translate-y-2 hover:shadow-xl relative group overflow-hidden bg-white/80 dark:bg-gray-800/80">
                    <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-primary-400/10 to-transparent rounded-bl-full"></div>

                    <h5 class="text-xl font-bold mb-6 flex items-center relative z-10">
                        <div class="tech-icon-container mr-3 bg-primary-50 p-2 rounded-lg">
                            <i class="fas fa-share-alt text-primary-600"></i>
                        </div>
                        <span>媒体平台</span>
                    </h5>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 relative z-10" id="platforms-cards-container">
                        <!-- 媒体平台卡片将在这里动态生成 -->
                        <div class="loading col-span-full flex flex-col items-center justify-center py-12">
                            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
                            <p class="mt-4 text-gray-500 dark:text-gray-400">加载中...</p>
                        </div>
                    </div>
                </div>

                <!-- 联系方式 -->
                <div class="glass-card rounded-xl p-6 card-3d transform transition-all duration-500 hover:-translate-y-2 hover:shadow-xl relative group overflow-hidden bg-white/80 dark:bg-gray-800/80">
                    <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-primary-400/10 to-transparent rounded-bl-full"></div>

                    <h5 class="text-xl font-bold mb-6 flex items-center relative z-10">
                        <div class="tech-icon-container mr-3 bg-primary-50 p-2 rounded-lg">
                            <i class="fas fa-envelope text-primary-600"></i>
                        </div>
                        <span>联系方式</span>
                        <div class="ml-2 px-2 py-0.5 bg-primary/10 rounded-full text-xs font-medium text-primary">通讯</div>
                    </h5>

                    <div class="space-y-4 relative z-10" id="contact-container">
                        <!-- 联系方式卡片将在这里动态生成 -->
                    </div>
                </div>

                <!-- 群聊 -->
                <div class="glass-card rounded-xl p-6 card-3d transform transition-all duration-500 hover:-translate-y-2 hover:shadow-xl relative group overflow-hidden bg-white/80 dark:bg-gray-800/80">
                    <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-secondary-400/10 to-transparent rounded-bl-full"></div>

                    <h5 class="text-xl font-bold mb-6 flex items-center relative z-10">
                        <div class="tech-icon-container mr-3 bg-secondary-50 p-2 rounded-lg">
                            <i class="fas fa-users text-secondary-600"></i>
                        </div>
                        <span>官方群聊</span>
                        <div class="ml-2 px-2 py-0.5 bg-secondary/10 rounded-full text-xs font-medium text-secondary">社区</div>
                    </h5>

                    <div class="space-y-4 relative z-10" id="group-chat-container">
                        <!-- 群聊卡片将在这里动态生成 -->
                    </div>
                </div>
            </div>
        </div>
    </div>


 <!-- 页脚 -->
<footer class="relative overflow-hidden bg-gray-900 text-white py-20">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(37,117,252,0.15),transparent_50%),radial-gradient(circle_at_70%_60%,rgba(63,218,216,0.15),transparent_50%)]"></div>
    <div class="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-blue-500 via-cyan-400 to-teal-400"></div>
    
    <!-- 装饰元素 -->
    <div class="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-gray-700 to-transparent opacity-30"></div>
    <div class="absolute -top-20 left-1/4 w-40 h-40 bg-blue-500/5 rounded-full blur-3xl"></div>
    <div class="absolute -bottom-20 right-1/4 w-40 h-40 bg-teal-500/5 rounded-full blur-3xl"></div>
    
    <!-- 内容区域 -->
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <!-- 联系我们 - 基础信息板块 -->
            <div class="bg-gray-800/30 backdrop-blur-md p-6 rounded-xl border border-gray-700/50 shadow-lg hover:shadow-blue-500/20 transition-all duration-500">
                <h4 class="text-xl font-bold mb-4 bg-gradient-to-r from-blue-400 to-teal-400 bg-clip-text text-transparent">基础信息</h4>
                <ul class="space-y-4">
                    <!-- ICP备案信息 -->
                    <li class="flex items-center gap-3">
                        <div class="w-10 h-10 flex items-center justify-center rounded-lg bg-blue-500/20 border border-blue-500/30">
                            <i class="fas fa-file-alt text-blue-400"></i>
                        </div>
                        <div>
                            <a 
                                href="https://beian.miit.gov.cn" 
                                target="_blank" 
                                rel="noopener noreferrer"
                                class="hover:text-blue-400 transition-colors duration-300 flex items-center gap-1"
                            >
                                <span class="text-sm text-gray-300">ICP备案号:</span>
                                <span class="font-medium">辽ICP备2025056705号</span>
                            </a>
                        </div>
                    </li>

                    <!-- 公安备案信息 -->
                    <li class="flex items-center gap-3">
                        <div class="w-10 h-10 flex items-center justify-center rounded-lg bg-teal-500/20 border border-teal-500/30">
                            <i class="fas fa-shield-alt text-teal-400"></i>
                        </div>
                        <div>
                            <a 
                                href="https://beian.mps.gov.cn/#/query/webSearch?code=21122402000208" 
                                rel="noopener noreferrer" 
                                target="_blank"
                                class="hover:text-teal-400 transition-colors duration-300 flex items-center gap-1"
                            >
                                <span class="text-sm text-gray-300">公安网备号:</span>
                                <span class="font-medium">辽公网安备21122402000208号</span>
                            </a>
                        </div>
                    </li>

                    <!-- 网站地图 -->
                    <li class="flex items-center gap-3">
                        <div class="w-10 h-10 flex items-center justify-center rounded-lg bg-purple-500/20 border border-purple-500/30">
                            <i class="fas fa-sitemap text-purple-400"></i>
                        </div>
                        <div>
                            <a 
                                href="sitemap/sitemap.xml" 
                                target="_blank" 
                                rel="noopener noreferrer"
                                class="hover:text-purple-400 transition-colors duration-300 flex items-center gap-1"
                            >
                                <span class="text-sm text-gray-300">站点地图:</span>
                                <span class="font-medium">XML格式地图</span>
                            </a>
                        </div>
                    </li>
                </ul>
            </div>
            
            <!-- 快速链接 -->
            <div class="bg-gray-800/30 backdrop-blur-md p-6 rounded-xl border border-gray-700/50 shadow-lg hover:shadow-purple-500/20 transition-all duration-500">
                <h4 class="text-xl font-bold mb-4 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">快速链接</h4>
                <ul class="space-y-3">
                    <li>
                        <a href="#projects-section" class="flex items-center justify-between text-gray-300 hover:text-purple-400 transition-all duration-300 group">
                            <span>项目列表</span>
                            <i class="fas fa-arrow-right text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>
                        </a>
                    </li>
                    <li>
                        <a href="#sites-section" class="flex items-center justify-between text-gray-300 hover:text-purple-400 transition-all duration-300 group">
                            <span>其他分站</span>
                            <i class="fas fa-arrow-right text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>
                        </a>
                    </li>
                    <li>
                        <a href="#about-section" class="flex items-center justify-between text-gray-300 hover:text-purple-400 transition-all duration-300 group">
                            <span>关于我们</span>
                            <i class="fas fa-arrow-right text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>
                        </a>
                    </li>
                </ul>
            </div>
            
            <div class="bg-gray-800/30 backdrop-blur-md p-6 rounded-xl border border-gray-700/50 shadow-lg hover:shadow-cyan-500/20 transition-all duration-500">
                <h4 class="text-xl font-bold mb-4 bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">友情链接</h4>
                    <!-- 新增申请友情链接按钮 -->
                <button id="apply-link-btn" class="tech-button block bg-gray-700 hover:bg-gray-600 transition-all duration-300">
                    <i class="fas fa-link mr-1"></i> 申请友情链接
                </button>
                <a href="https://jilin9527.top/" class="tech-button block " target="_blank" rel="noopener noreferrer">
                    <i class="fas fa-external-link-alt mr-1"></i> JiLin的小窝
                </a>
            </div>
        </div>
        
        <!-- 底部版权信息 -->
        <div class="pt-8 border-t border-gray-800/50">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-400 text-sm mb-4 md:mb-0">
                    本网站由 <span class="font-medium text-gray-300">领创工作室</span> 提供技术支持
                </p>
                <div class="flex space-x-6">
                    <p class="mt-6 text-gray-400 text-sm">
                        &copy; 2020-2025 <span class="bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent font-medium">领创工作室</span>. 保留所有权利.
                    </p>
                </div>
            </div>
        </div>
    </div>
</footer>    

    <!-- 固定悬浮按钮区域 -->
    <div class="fixed bottom-6 left-1/2 transform -translate-x-1/2 flex flex-row space-x-3 z-40" id="floating-buttons">
        <a href="/jz" target="_blank" rel="noopener noreferrer" 
           class="floating-button bg-primary/90 backdrop-blur-sm text-white p-3 rounded-full shadow-neon-primary flex items-center justify-center hover:bg-primary transition-all duration-300 group" 
           title="捐赠我们">
            <span class="relative flex items-center justify-center">
                <span class="absolute inset-0 rounded-full bg-white/20 blur-sm group-hover:blur-md transition-all duration-300"></span>
                <i class="fas fa-gift relative z-10"></i>
                <span class="ml-2 max-w-0 overflow-hidden group-hover:max-w-xs transition-all duration-500 ease-in-out whitespace-nowrap relative z-10">捐赠我们</span>
            </span>
        </a>
        <a href="https://lacs.cc/yc" 
           class="floating-button bg-secondary/90 backdrop-blur-sm text-white p-3 rounded-full shadow-neon-secondary flex items-center justify-center hover:bg-secondary transition-all duration-300 group" 
           data-umami-event="访问远程刷机" 
           title="远程刷机">
            <span class="relative flex items-center justify-center">
                <span class="absolute inset-0 rounded-full bg-white/20 blur-sm group-hover:blur-md transition-all duration-300"></span>
                <i class="fas fa-mobile-alt relative z-10"></i>
                <span class="ml-2 max-w-0 overflow-hidden group-hover:max-w-xs transition-all duration-500 ease-in-out whitespace-nowrap relative z-10">远程刷机</span>
            </span>
        </a>
        <button id="back-to-top" 
                class="floating-button bg-gray-700/90 backdrop-blur-sm text-white p-3 rounded-full shadow-lg flex items-center justify-center hover:bg-gray-800 hover:shadow-neon-accent transition-all duration-300 group" 
                title="返回顶部">
            <span class="relative flex items-center justify-center">
                <span class="absolute inset-0 rounded-full bg-white/10 blur-sm group-hover:blur-md transition-all duration-300"></span>
                <i class="fas fa-arrow-up relative z-10 group-hover:animate-bounce-slow"></i>
            </span>
        </button>
    </div>

    <!-- 模态框 -->
    <div id="modal" class="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 hidden">
        <div class="bg-white/95 dark:bg-gray-800/95 rounded-xl border border-gray-200 dark:border-gray-700 shadow-neon-accent dark:shadow-neon-primary tech-border backdrop-blur-md max-w-lg w-full mx-4 transform transition-all duration-300 scale-95 opacity-0" id="modal-content">
            <!-- 装饰性网格背景 -->
            <div class="absolute inset-0 overflow-hidden rounded-xl pointer-events-none">
                <div class="absolute inset-0 bg-grid-pattern opacity-10"></div>
            
            </div>
            
            <div class="p-6 relative z-10">
                <div class="flex justify-between items-center mb-6 border-b border-gray-200 dark:border-gray-700 pb-3">
                    <div class="flex items-center">
                        <span class="inline-block w-3 h-3 bg-primary rounded-full mr-2 animate-pulse-slow"></span>
                        <h3 class="text-xl font-bold bg-gradient-text" id="modal-title">详情</h3>
                    </div>
                    <button id="close-modal" class="text-gray-500 hover:text-gray-700 hover:rotate-90 transition-all duration-300 w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-200 dark:hover:bg-gray-700">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                <div class="mb-6" id="modal-body">
                    <div class="relative overflow-hidden rounded-lg mb-4 tech-border">
                        <img id="modal-image" src="" alt="" class="max-w-full h-auto rounded-lg transition-transform duration-500 hover:scale-105">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                    </div>
                    <p id="modal-description" class="mt-4 text-gray-600 dark:text-gray-300 leading-relaxed"></p>
                </div>
            </div>
        </div>
    </div>
<!-- 友情链接申请模态框 -->
<div id="link-modal" class="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 hidden">
    <div class="bg-white/95 dark:bg-gray-800/95 rounded-xl border border-gray-200 dark:border-gray-700 shadow-neon-accent dark:shadow-neon-primary tech-border backdrop-blur-md max-w-lg w-full mx-4 transform transition-all duration-300 scale-95 opacity-0" id="link-modal-content">
        <div class="absolute inset-0 overflow-hidden rounded-xl pointer-events-none">
            <div class="absolute inset-0 bg-grid-pattern opacity-10"></div>
        </div>
        
        <div class="p-6 relative z-10">
            <div class="flex justify-between items-center mb-6 border-b border-gray-200 dark:border-gray-700 pb-3">
                <div class="flex items-center">
                    <span class="inline-block w-3 h-3 bg-primary rounded-full mr-2 animate-pulse-slow"></span>
                    <h3 class="text-xl font-bold bg-gradient-text">申请友情链接</h3>
                </div>
                <button id="close-link-modal" class="text-gray-500 hover:text-gray-700 hover:rotate-90 transition-all duration-300 w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-200 dark:hover:bg-gray-700">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div class="mb-6">
                <p class="text-gray-600 dark:text-gray-300 mb-4">请将以下网站基本信息发送至：<strong><EMAIL></strong></p>
                <ul class="list-disc list-inside text-gray-600 dark:text-gray-300 space-y-2">
                    <li>网站名称</li>
                    <li>网站URL地址</li>
                    <li>网站简介</li>
                    <li>网站Logo（200x200px）</li>
                    <li>您的联系方式</li>
                </ul>
                <div class="mt-6 p-4 bg-primary-50 dark:bg-primary-900/20 rounded-lg border border-primary-100 dark:border-primary-800">
                    <p class="text-sm text-gray-600 dark:text-gray-300"><i class="fas fa-info-circle text-primary mr-2"></i> 我们会在3-5个工作日内审核并回复您的申请</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="js/main.js"></script>
<script defer src="https://umami.lacs.cc/script.js" data-website-id="a4e8c20f-d2e8-4b10-bdf5-2d52c389fd45"></script>
</body>
</html>