import { getPermalink, getBlogPermalink, getAsset } from './utils/permalinks';

export const headerData = {
  links: [
    {
      text: '首页',
      href: getPermalink('/'),
    },
    {
      text: '项目展示',
      href: getPermalink('/#projects'),
    },
    {
      text: '远程刷机',
      href: getPermalink('/remote-flash'),
    },
    {
      text: '关于我们',
      href: getPermalink('/#about'),
    },
    {
      text: '联系我们',
      href: getPermalink('/contact'),
    },
    {
      text: '捐赠我们',
      href: getPermalink('/donate'),
    },
  ],
  actions: [],
};

export const footerData = {
  links: [
    {
      title:'网站信息',
      links:[
        {text:'公安网备',herf:'https://beian.miit.gov.cn/'}
        ,{text:'ICP备案',herf:'https://beian.miit.gov.cn/'}
      ]
    },
    {
      title: '服务',
      links: [
        { text: '远程刷机', href: getPermalink('/remote-flash') },
        { text: '技术支持', href: getPermalink('/contact') },
        { text: '项目展示', href: getPermalink('/#projects') },
        { text: '关于我们', href: getPermalink('/#about') },
        { text: '捐赠我们', href: getPermalink('/donate') },
      ],
    },
  ],
  secondaryLinks: [
    { text: '服务条款', href: '#' },
    { text: '隐私政策', href: '#' },
  ],
  socialLinks: [
    { ariaLabel: '站点地图', icon: 'tabler:sitemap', href: '/sitemap-index.xml' },
  ],
  footNote: `
    <span class="text-sm">© 2020-2025 领创工作室 (Lead And Create Studio) · 保留所有权利</span>
  `,
};
