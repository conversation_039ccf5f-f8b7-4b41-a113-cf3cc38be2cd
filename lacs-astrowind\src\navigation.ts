import { getPermalink, getBlogPermalink, getAsset } from './utils/permalinks';

export const headerData = {
  links: [
    {
      text: '首页',
      href: getPermalink('/'),
    },
    {
      text: '项目展示',
      href: getPermalink('/#projects'),
    },
    {
      text: '远程刷机',
      href: getPermalink('/remote-flash'),
    },
    {
      text: '关于我们',
      href: getPermalink('/#about'),
    },
    {
      text: '联系我们',
      href: getPermalink('/contact'),
    },
    {
      text: '捐赠我们',
      href: getPermalink('/donate'),
    },
  ],
  actions: [],
};

export const footerData = {
  links: [
    {
      title: '网站信息',
      links: [
        {
          text: '<i class="fas fa-file-alt mr-2"></i>ICP备案: 辽ICP备2025056705号',
          href: 'https://beian.miit.gov.cn/',
          ariaLabel: 'ICP备案信息'
        },
        {
          text: '<i class="fas fa-shield-alt mr-2"></i>公安网备: 辽公网安备21122402000208号',
          href: 'https://beian.mps.gov.cn/#/query/webSearch?code=21122402000208',
          ariaLabel: '公安网备案信息'
        }
      ]
    },
    {
      title: '服务',
      links: [
        { text: '远程刷机', href: getPermalink('/remote-flash') },
        { text: '技术支持', href: getPermalink('/contact') },
        { text: '项目展示', href: getPermalink('/#projects') },
        { text: '关于我们', href: getPermalink('/#about') },
        { text: '捐赠我们', href: getPermalink('/donate') },
      ],
    },
  ],
  secondaryLinks: [
    { text: '服务条款', href: '#' },
    { text: '隐私政策', href: '#' },
  ],
  socialLinks: [
    { ariaLabel: '站点地图', icon: 'tabler:sitemap', href: '/sitemap-index.xml' },
  ],
  footNote: `
    <div class="flex flex-col md:flex-row items-center justify-between gap-4">
      <span class="text-sm">© 2020-2025 领创工作室 (Lead And Create Studio) · 保留所有权利</span>
      <div class="flex flex-col md:flex-row gap-2 md:gap-4 text-xs text-muted">
        <a href="https://beian.miit.gov.cn/" target="_blank" rel="noopener noreferrer" class="hover:text-primary transition-colors">
          <i class="fas fa-file-alt mr-1"></i>辽ICP备2025056705号
        </a>
        <a href="https://beian.mps.gov.cn/#/query/webSearch?code=21122402000208" target="_blank" rel="noopener noreferrer" class="hover:text-primary transition-colors">
          <i class="fas fa-shield-alt mr-1"></i>辽公网安备21122402000208号
        </a>
      </div>
    </div>
  `,
};
