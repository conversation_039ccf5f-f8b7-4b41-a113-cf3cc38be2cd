---
import WidgetWrapper from '~/components/ui/WidgetWrapper.astro';
import type { CallToAction, Widget } from '~/types';
import Headline from '~/components/ui/Headline.astro';
import Button from '~/components/ui/Button.astro';

interface Props extends Widget {
  title?: string;
  subtitle?: string;
  tagline?: string;
  callToAction?: CallToAction;
  actions?: string | CallToAction[];
}

const {
  title = await Astro.slots.render('title'),
  subtitle = await Astro.slots.render('subtitle'),
  tagline = await Astro.slots.render('tagline'),
  actions = await Astro.slots.render('actions'),

  id,
  isDark = false,
  classes = {},
  bg = await Astro.slots.render('bg'),
} = Astro.props;
---

<WidgetWrapper id={id} isDark={isDark} containerClass={`max-w-6xl mx-auto ${classes?.container ?? ''}`} bg={bg}>
  <div
    class="max-w-3xl mx-auto text-center p-6 rounded-md shadow-xl dark:shadow-none dark:border dark:border-slate-600"
  >
    <Headline
      title={title}
      subtitle={subtitle}
      tagline={tagline}
      classes={{
        container: 'mb-0 md:mb-0',
        title: 'text-4xl md:text-4xl font-bold tracking-tighter mb-4 font-heading',
        subtitle: 'text-xl text-muted dark:text-slate-400',
      }}
    />
    {
      actions && (
        <div class="max-w-xs sm:max-w-md m-auto flex flex-nowrap flex-col sm:flex-row sm:justify-center gap-4 mt-6">
          {Array.isArray(actions) ? (
            actions.map((action) => (
              <div class="flex w-full sm:w-auto">
                <Button {...(action || {})} class="w-full sm:mb-0" />
              </div>
            ))
          ) : (
            <Fragment set:html={actions} />
          )}
        </div>
      )
    }
  </div>
</WidgetWrapper>
